class Api::V1::Widget::ConversationsController < Api::V1::Widget::BaseController
  include Events::Types
  before_action :render_not_found_if_empty, only: [:toggle_typing, :toggle_status, :set_custom_attributes, :destroy_custom_attributes, :bot_handoff, :destroy]

  def index
    @conversation = conversation
  end

  def create
    ActiveRecord::Base.transaction do
      process_update_contact
      @conversation = create_conversation
      conversation.messages.create!(message_params)
      # TODO: Temporary fix for message type cast issue, since message_type is returning as string instead of integer
      conversation.reload
    end
  end

  def process_update_contact
    @contact = ContactIdentifyAction.new(
      contact: @contact,
      params: { email: contact_email, phone_number: contact_phone_number, name: contact_name },
      retain_original_contact_name: true,
      discard_invalid_attrs: true
    ).perform
  end

  def update_last_seen
    head :ok && return if conversation.nil?

    conversation.update(contact_last_seen_at: DateTime.now.utc)
    conversation.save!
    ::Conversations::UpdateMessageStatusJob.perform_later(conversation.id, conversation.contact_last_seen_at)
    head :ok
  end

  def list
    Rails.logger.info "Current @contact: #{@contact.inspect}" # 调试信息
    if conversations.present? && conversations.first.present?
      Rails.logger.info "First conversation's contact: #{conversations.first.contact.inspect}" # 调试信息
    end

    render json: conversations.map { |conversation|
      conversation_attributes = conversation.as_json
      target_message = conversation.messages.non_activity_messages.where(private: false).first
      last_message_data = target_message.try(:push_event_data)
      conversation_json = conversation_attributes.merge('last_non_activity_message' => last_message_data)

      conversation_json['meta'] = build_conversation_meta(conversation)
      conversation_json
    }
  end

  def transcript
    if conversation.present? && conversation.contact.present? && conversation.contact.email.present?
      ConversationReplyMailer.with(account: conversation.account).conversation_transcript(
        conversation,
        conversation.contact.email
      )&.deliver_later
    end
    head :ok
  end

  def toggle_typing
    case permitted_params[:typing_status]
    when 'on'
      trigger_typing_event(CONVERSATION_TYPING_ON)
    when 'off'
      trigger_typing_event(CONVERSATION_TYPING_OFF)
    end

    head :ok
  end

  def toggle_status
    return head :forbidden unless @web_widget.end_conversation?

    unless conversation.resolved?
      conversation.resolved!
    end
    head :ok
  end

  def set_custom_attributes
    conversation.update!(custom_attributes: permitted_params[:custom_attributes])
  end

  def destroy_custom_attributes
    conversation.custom_attributes = conversation.custom_attributes.excluding(params[:custom_attribute])
    conversation.save!
    render json: conversation
  end

  def bot_handoff
    if conversation.present?
      conversation.bot_handoff!

      detected_language = Captain::WorkingHoursService.get_target_language(conversation)

      message = get_handoff_message_for_language(detected_language)

      # 如果不在工作时间内，拼接工作时间信息
      working_hours_text = Captain::WorkingHoursService.get_working_hours_text(conversation, detected_language)
      if working_hours_text.present?
        message = "#{message}\n\n#{working_hours_text}"
      end

      conversation.messages.create!(
        message_type: :outgoing,
        account_id: conversation.account.id,
        inbox_id: conversation.inbox.id,
        content: message
      )

      render json: { success: true, conversation_status: conversation.status }, status: :ok
    else
      render_not_found_if_empty
    end
  end

  def destroy
    return head :not_found if conversation.nil?

    # 检查是否已经被删除
    if conversation.deleted?
      return render json: { error: 'Conversation already deleted' }, status: :gone
    end

    # 只允许删除已关闭的会话
    Rails.logger.info "Conversation #{conversation.id} is #{conversation.status}"
    unless conversation.resolved?
      Rails.logger.info "Conversation #{conversation.id} is not resolved"
      return render json: { error: 'Only resolved conversations can be deleted' }, status: :unprocessable_entity
    end

    # 检查会话是否属于当前联系人
    unless conversation.contact == @contact
      return render json: { error: 'Unauthorized' }, status: :unauthorized
    end

    conversation.soft_delete!
    head :no_content
  rescue StandardError => e
    render json: { error: e.message }, status: :internal_server_error
  end

  private

  def build_conversation_meta(conversation)
    {
      sender: build_sender_meta,
      assignee: build_assignee_meta(conversation.assignee, conversation.account_id),
      channel: conversation.inbox.try(:channel_type),
      hmac_verified: conversation.contact_inbox&.hmac_verified,
      unread_count: conversation.unread_outgoing_messages.count
    }
  end

  def build_sender_meta
    {
      additional_attributes: @contact.additional_attributes,
      availability_status: @contact.availability_status,
      email: @contact.email,
      id: @contact.id,
      name: @contact.name,
      phone_number: @contact.phone_number,
      blocked: @contact.blocked,
      identifier: @contact.identifier,
      thumbnail: @contact.avatar_url,
      custom_attributes: @contact.custom_attributes,
      last_activity_at: @contact.last_activity_at.to_i,
      created_at: @contact.created_at.to_i
    }
  end

  def build_assignee_meta(assignee, conversation_account_id)
    return nil if assignee.blank?

    assignee_role = nil
    # Try to find role using conversation_account_id context
    if assignee.respond_to?(:account_users) && conversation_account_id.present?
      assignee_role = assignee.account_users.find_by(account_id: conversation_account_id)&.role
    end

    # Fallback for SuperAdmin or if role not found in specific account context
    if assignee_role.nil? && assignee.is_a?(SuperAdmin)
      assignee_role = assignee.role if assignee.respond_to?(:role) # Check if SuperAdmin model has a 'role' attribute
      assignee_role ||= 'super_admin' # Default role for SuperAdmin
    end

    # Determine the account_id for the assignee's metadata.
    # For SuperAdmin, this might not be their primary account or could be nil.
    meta_assignee_account_id = nil
    if assignee.respond_to?(:accounts) && assignee.accounts.present?
      meta_assignee_account_id = assignee.accounts.first&.id
    elsif !assignee.is_a?(SuperAdmin) && conversation_account_id.present?
      # For non-SuperAdmins, if accounts.first is not available, maybe use conversation_account_id
      meta_assignee_account_id = conversation_account_id
    end

    {
      id: assignee.id,
      account_id: meta_assignee_account_id,
      availability_status: assignee.availability_status,
      auto_offline: true,
      confirmed: assignee.confirmed?,
      email: assignee.email,
      available_name: assignee.available_name,
      name: assignee.name,
      role: assignee_role,
      thumbnail: assignee.avatar_url,
      custom_role_id: nil
    }
  end

  def trigger_typing_event(event)
    Rails.configuration.dispatcher.dispatch(event, Time.zone.now, conversation: conversation, user: @contact)
  end

  def render_not_found_if_empty
    return head :not_found if conversation.nil?
  end

  def permitted_params
    params.permit(:id, :typing_status, :website_token, :email, :conversation_id, contact: [:name, :email, :phone_number],
                                                                                 message: [:content, :referer_url, :timestamp, :echo_id],
                                                                                 custom_attributes: {})
  end

  def get_handoff_message_for_language(language)
    Captain::LanguageMapper.get_handoff_message_for_language(language)
  end
end
