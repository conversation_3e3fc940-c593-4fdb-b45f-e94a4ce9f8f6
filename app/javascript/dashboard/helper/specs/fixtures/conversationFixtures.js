export const conversationData = {
  meta: {
    sender: {
      additional_attributes: {
        created_at_ip: '127.0.0.1',
      },
      availability_status: 'offline',
      email: null,
      id: 5017687,
      name: 'long-flower-143',
      phone_number: null,
      thumbnail: '',
      custom_attributes: {},
    },
    channel: 'Channel::WebWidget',
    assignee: {
      account_id: 1,
      availability_status: 'offline',
      confirmed: true,
      email: '<EMAIL>',
      available_name: '<PERSON><PERSON><PERSON>',
      id: 21,
      name: '<PERSON><PERSON><PERSON>',
      role: 'administrator',
      thumbnail: 'http://example.com/image.png',
    },
  },
  id: 5815,
  messages: [
    {
      id: 438072,
      content: 'Campaign after 5 seconds',
      account_id: 1,
      inbox_id: 37,
      conversation_id: 5811,
      message_type: 1,
      created_at: **********,
      updated_at: '2021-05-14T08:17:42.041Z',
      private: false,
      status: 'sent',
      source_id: null,
      content_type: null,
      content_attributes: {},
      sender_type: 'User',
      sender_id: 1,
      external_source_ids: {},
    },
    {
      id: **********,
      content: 'Hello',
      account_id: 1,
      inbox_id: 37,
      conversation_id: 5815,
      message_type: 0,
      created_at: **********,
      updated_at: '2021-05-16T05:48:43.910Z',
      private: false,
      status: 'sent',
      source_id: null,
      content_type: 'text',
      content_attributes: {},
      sender_type: null,
      sender_id: null,
      external_source_ids: {},
    },
    {
      id: 438100,
      content: 'Hey',
      account_id: 1,
      inbox_id: 37,
      conversation_id: 5815,
      message_type: 0,
      created_at: **********,
      updated_at: '2021-05-16T05:48:43.910Z',
      private: false,
      status: 'sent',
      source_id: null,
      content_type: 'text',
      content_attributes: {},
      sender_type: null,
      sender_id: null,
      external_source_ids: {},
    },
  ],
  inbox_id: 37,
  status: 'open',
  muted: false,
  can_reply: true,
  timestamp: **********,
  contact_last_seen_at: 0,
  agent_last_seen_at: **********,
  unread_count: 0,
  additional_attributes: {
    browser: {
      device_name: 'Unknown',
      browser_name: 'Chrome',
      platform_name: 'macOS',
      browser_version: '90.0.4430.212',
      platform_version: '10.15.7',
    },
    widget_language: null,
    browser_language: 'en',
  },
  account_id: 1,
  labels: [],
};

export const lastMessageData = {
  id: 438100,
  content: 'Hey',
  account_id: 1,
  inbox_id: 37,
  conversation_id: 5815,
  message_type: 0,
  created_at: **********,
  updated_at: '2021-05-16T05:48:43.910Z',
  private: false,
  status: 'sent',
  source_id: null,
  content_type: 'text',
  content_attributes: {},
  sender_type: null,
  sender_id: null,
  external_source_ids: {},
};

export const readMessagesData = [
  {
    id: 438072,
    content: 'Campaign after 5 seconds',
    account_id: 1,
    inbox_id: 37,
    conversation_id: 5811,
    message_type: 1,
    created_at: **********,
    updated_at: '2021-05-14T08:17:42.041Z',
    private: false,
    status: 'sent',
    source_id: null,
    content_type: null,
    content_attributes: {},
    sender_type: 'User',
    sender_id: 1,
    external_source_ids: {},
  },
];

export const unReadMessagesData = [
  {
    id: **********,
    content: 'Hello',
    account_id: 1,
    inbox_id: 37,
    conversation_id: 5815,
    message_type: 0,
    created_at: **********,
    updated_at: '2021-05-16T05:48:43.910Z',
    private: false,
    status: 'sent',
    source_id: null,
    content_type: 'text',
    content_attributes: {},
    sender_type: null,
    sender_id: null,
    external_source_ids: {},
  },
  {
    id: 438100,
    content: 'Hey',
    account_id: 1,
    inbox_id: 37,
    conversation_id: 5815,
    message_type: 0,
    created_at: **********,
    updated_at: '2021-05-16T05:48:43.910Z',
    private: false,
    status: 'sent',
    source_id: null,
    content_type: 'text',
    content_attributes: {},
    sender_type: null,
    sender_id: null,
    external_source_ids: {},
  },
];
