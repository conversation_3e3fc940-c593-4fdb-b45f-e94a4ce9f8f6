export default [
  {
    additionalAttributes: {
      city: 'Los Angeles',
      country: 'United States',
      description:
        "I'm <PERSON><PERSON>, a developer focusing on building web solutions. Currently, I’m working as a Product Developer at AI Agent-OK.",
      companyName: 'AI Agent-OK',
      countryCode: 'US',
      socialProfiles: {
        github: 'candice-dev',
        twitter: 'candice_w_dev',
        facebook: 'candice.dev',
        linkedin: 'candice-matherson',
        instagram: 'candice.codes',
      },
    },
    availabilityStatus: 'offline',
    email: '<EMAIL>',
    id: 22,
    name: '<PERSON><PERSON>',
    phoneNumber: '+14155552671',
    identifier: null,
    thumbnail: '',
    customAttributes: {
      dateContact: '2024-11-11T11:53:09.299Z',
      linkContact: 'https://example.com',
      listContact: 'Follow-Up',
      textContact: 'Hi there!',
      numberContact: '42',
      checkboxContact: false,
    },
    lastActivityAt: 1712123233,
    createdAt: 1712123233,
  },
  {
    additionalAttributes: {
      city: 'San Francisco',
      country: 'United States',
      description: 'Passionate about design and user experience.',
      companyName: 'Designify',
      countryCode: 'US',
      socialProfiles: {
        github: 'ophelia-folkard',
        twitter: 'oph_designs',
        facebook: 'ophelia.folkard',
        linkedin: 'ophelia-folkard',
        instagram: 'ophelia.design',
      },
    },
    availabilityStatus: 'offline',
    email: '<EMAIL>',
    id: 21,
    name: 'Ophelia Folkard',
    phoneNumber: '+14155552672',
    identifier: null,
    thumbnail: '',
    customAttributes: {
      dateContact: '2024-10-05T10:12:34.567Z',
      linkContact: 'https://designify.com',
      listContact: 'Prospects',
      textContact: 'Looking forward to connecting!',
    },
    lastActivityAt: 1712123233,
    createdAt: 1712123233,
  },
  {
    additionalAttributes: {
      city: 'Austin',
      country: 'United States',
      description: 'Avid coder and tech enthusiast.',
      companyName: 'CodeHub',
      countryCode: 'US',
      socialProfiles: {
        github: 'willy_castelot',
        twitter: 'willy_code',
        facebook: 'willy.castelot',
        linkedin: 'willy-castelot',
        instagram: 'willy.coder',
      },
    },
    availabilityStatus: 'offline',
    email: '<EMAIL>',
    id: 20,
    name: 'Willy Castelot',
    phoneNumber: '+14155552673',
    identifier: null,
    thumbnail: '',
    customAttributes: {
      textContact: 'Let’s collaborate!',
      checkboxContact: true,
    },
    lastActivityAt: 1712123233,
    createdAt: 1712123233,
  },
  {
    additionalAttributes: {
      city: 'Seattle',
      country: 'United States',
      description: 'Product manager with a love for innovation.',
      companyName: 'InnovaTech',
      countryCode: 'US',
      socialProfiles: {
        github: 'elisabeth-d',
        twitter: 'elisabeth_innova',
        facebook: 'elisabeth.derington',
        linkedin: 'elisabeth-derington',
        instagram: 'elisabeth.innovates',
      },
    },
    availabilityStatus: 'offline',
    email: '<EMAIL>',
    id: 19,
    name: 'Elisabeth Derington',
    phoneNumber: '+14155552674',
    identifier: null,
    thumbnail: '',
    customAttributes: {
      textContact: 'Let’s schedule a call.',
    },
    lastActivityAt: 1712123232,
    createdAt: 1712123232,
  },
  {
    additionalAttributes: {
      city: 'Chicago',
      country: 'United States',
      description: 'Marketing specialist and content creator.',
      companyName: 'Contently',
      countryCode: 'US',
      socialProfiles: {
        github: 'olia-olenchenko',
        twitter: 'olia_content',
        facebook: 'olia.olenchenko',
        linkedin: 'olia-olenchenko',
        instagram: 'olia.creates',
      },
    },
    availabilityStatus: 'offline',
    email: '<EMAIL>',
    id: 18,
    name: 'Olia Olenchenko',
    phoneNumber: '+14155552675',
    identifier: null,
    thumbnail: '',
    customAttributes: {},
    lastActivityAt: 1712123232,
    createdAt: 1712123232,
  },
  {
    additionalAttributes: {
      city: 'Boston',
      country: 'United States',
      description: 'SEO expert and analytics enthusiast.',
      companyName: 'OptiSearch',
      countryCode: 'US',
      socialProfiles: {
        github: 'nate-vannuchi',
        twitter: 'nate_seo',
        facebook: 'nathaniel.vannuchi',
        linkedin: 'nathaniel-vannuchi',
        instagram: 'nate.optimizes',
      },
    },
    availabilityStatus: 'offline',
    email: '<EMAIL>',
    id: 17,
    name: 'Nathaniel Vannuchi',
    phoneNumber: '+14155552676',
    identifier: null,
    thumbnail: '',
    customAttributes: {},
    lastActivityAt: 1712123232,
    createdAt: 1712123232,
  },
  {
    additionalAttributes: {
      city: 'Denver',
      country: 'United States',
      description: 'UI/UX designer with a flair for minimalist designs.',
      companyName: 'Minimal Designs',
      countryCode: 'US',
      socialProfiles: {
        github: 'merrile-petruk',
        twitter: 'merrile_ux',
        facebook: 'merrile.petruk',
        linkedin: 'merrile-petruk',
        instagram: 'merrile.designs',
      },
    },
    availabilityStatus: 'offline',
    email: '<EMAIL>',
    id: 16,
    name: 'Merrile Petruk',
    phoneNumber: '+14155552677',
    identifier: null,
    thumbnail: '',
    customAttributes: {},
    lastActivityAt: 1712123232,
    createdAt: 1712123232,
  },
  {
    additionalAttributes: {
      city: 'Miami',
      country: 'United States',
      description: 'Entrepreneur with a background in e-commerce.',
      companyName: 'Ecom Solutions',
      countryCode: 'US',
      socialProfiles: {
        github: 'cordell-d',
        twitter: 'cordell_entrepreneur',
        facebook: 'cordell.dalinder',
        linkedin: 'cordell-dalinder',
        instagram: 'cordell.ecom',
      },
    },
    availabilityStatus: 'offline',
    email: '<EMAIL>',
    id: 15,
    name: 'Cordell Dalinder',
    phoneNumber: '+14155552678',
    identifier: null,
    thumbnail: '',
    customAttributes: {},
    lastActivityAt: 1712123232,
    createdAt: 1712123232,
  },
];
