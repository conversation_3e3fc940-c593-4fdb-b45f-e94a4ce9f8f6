<script setup>
import { computed, ref, onMounted, nextTick, watch } from 'vue';
import {
  appendSignature,
  removeSignature,
  extractTextFromMarkdown,
} from 'dashboard/helper/editorHelper';

const props = defineProps({
  modelValue: { type: String, default: '' },
  label: { type: String, default: '' },
  placeholder: { type: String, default: '' },
  maxLength: { type: Number, default: 200 },
  id: { type: String, default: '' },
  disabled: { type: Boolean, default: false },
  customTextAreaClass: { type: String, default: '' },
  customTextAreaWrapperClass: { type: String, default: '' },
  showCharacterCount: { type: Boolean, default: false },
  autoHeight: { type: Boolean, default: false },
  resize: { type: Boolean, default: false },
  minHeight: { type: String, default: '4rem' },
  maxHeight: { type: String, default: '12rem' },
  autofocus: { type: <PERSON>olean, default: false },
  message: { type: String, default: '' },
  messageType: {
    type: String,
    default: 'info',
    validator: value => ['info', 'error', 'success'].includes(value),
  },
  signature: { type: String, default: '' },
  sendWithSignature: { type: Boolean, default: false }, // add this as a prop, so that we won't have to add useUISettings
  allowSignature: { type: Boolean, default: false }, // allowSignature is a kill switch, ensuring no signature methods are triggered except when this flag is true
  mentionOptions: { type: Array, default: () => [] }, // 添加mentionOptions属性，用于提及功能
});

const emit = defineEmits(['update:modelValue']);

const textareaRef = ref(null);
const isFocused = ref(false);
const showMentionsList = ref(false);
const mentionPosition = ref({ top: 0, left: 0 });
const filteredMentions = ref([]);
const mentionSearchQuery = ref('');
const highlightedMentionIndex = ref(0);

const characterCount = computed(() => props.modelValue.length);
const cleanedSignature = computed(() =>
  extractTextFromMarkdown(props.signature)
);

const messageClass = computed(() => {
  switch (props.messageType) {
    case 'error':
      return 'text-n-ruby-9 dark:text-n-ruby-9';
    case 'success':
      return 'text-green-500 dark:text-green-400';
    default:
      return 'text-n-slate-11 dark:text-n-slate-11';
  }
});

// TODO - use "field-sizing: content" and "height: auto" in future for auto height, when available.
const adjustHeight = () => {
  if (!props.autoHeight || !textareaRef.value) return;

  // Reset height to auto to get the correct scrollHeight
  textareaRef.value.style.height = 'auto';
  // Set the height to the scrollHeight
  textareaRef.value.style.height = `${textareaRef.value.scrollHeight}px`;
};

const setCursor = () => {
  if (!textareaRef.value) return;

  const bodyWithoutSignature = removeSignature(
    props.modelValue,
    cleanedSignature.value
  );
  const bodyEndsAt = bodyWithoutSignature.trimEnd().length;

  textareaRef.value.focus();
  textareaRef.value.setSelectionRange(bodyEndsAt, bodyEndsAt);
};

const toggleSignatureInEditor = signatureEnabled => {
  if (!props.allowSignature) return;
  const valueWithSignature = signatureEnabled
    ? appendSignature(props.modelValue, cleanedSignature.value)
    : removeSignature(props.modelValue, cleanedSignature.value);
  emit('update:modelValue', valueWithSignature);

  nextTick(() => {
    adjustHeight();
    setCursor();
  });
};

// 计算光标位置的辅助函数
const getCaretCoordinates = (element, position) => {
  // 创建临时元素用于计算
  const div = document.createElement('div');
  div.style.position = 'absolute';
  div.style.visibility = 'hidden';
  div.style.whiteSpace = 'pre-wrap';
  div.style.wordWrap = 'break-word';
  div.style.overflow = 'hidden';
  div.style.width = `${element.clientWidth}px`;
  div.style.height = 'auto';

  // 复制样式
  const computedStyle = window.getComputedStyle(element);
  const stylesToCopy = [
    'fontFamily',
    'fontSize',
    'fontWeight',
    'lineHeight',
    'letterSpacing',
    'textIndent',
    'wordSpacing',
    'paddingLeft',
    'paddingRight',
    'paddingTop',
    'paddingBottom',
    'borderLeftWidth',
    'borderRightWidth',
    'borderTopWidth',
    'borderBottomWidth',
  ];

  stylesToCopy.forEach(prop => {
    div.style[prop] = computedStyle[prop];
  });

  // 计算位置
  const textBeforeCaret = element.value.substring(0, position);
  div.textContent = textBeforeCaret;

  // 添加一个 span 用于确定光标位置
  const span = document.createElement('span');
  span.textContent = '.';
  div.appendChild(span);

  document.body.appendChild(div);

  const coordinates = {
    top: span.offsetTop,
    left: span.offsetLeft,
  };

  document.body.removeChild(div);
  return coordinates;
};

// 检查 @ 提及功能
const checkForMention = text => {
  const cursorPosition = textareaRef.value?.selectionStart || 0;
  const textBeforeCursor = text.substring(0, cursorPosition);
  const mentionMatch = textBeforeCursor.match(/@([^\s@]*)$/);

  if (mentionMatch) {
    const query = mentionMatch[1].toLowerCase();
    mentionSearchQuery.value = query;

    // 过滤提及选项
    filteredMentions.value = props.mentionOptions.filter(option =>
      option.toLowerCase().includes(query)
    );

    if (filteredMentions.value.length > 0) {
      showMentionsList.value = true;
      highlightedMentionIndex.value = 0; // Reset highlight when list appears or changes
      // 计算提及弹窗位置
      nextTick(() => {
        const textarea = textareaRef.value;
        if (!textarea) return;

        const cursorPos = getCaretCoordinates(textarea, cursorPosition);

        mentionPosition.value = {
          top: cursorPos.top + textarea.scrollTop,
          left: cursorPos.left,
        };
      });
    } else {
      showMentionsList.value = false;
    }
  } else {
    showMentionsList.value = false;
  }
};

// 处理选择提及选项
const selectMention = mention => {
  const cursorPosition = textareaRef.value?.selectionStart || 0;
  const textBeforeCursor = props.modelValue.substring(0, cursorPosition);
  const textAfterCursor = props.modelValue.substring(cursorPosition);

  // 找到 @ 符号位置
  const lastAtPos = textBeforeCursor.lastIndexOf('@');
  if (lastAtPos >= 0) {
    // 替换 @query 为选中的 @mention
    const newTextBeforeCursor =
      textBeforeCursor.substring(0, lastAtPos) + '@' + mention + ' ';
    const newValue = newTextBeforeCursor + textAfterCursor;
    emit('update:modelValue', newValue);

    // 关闭提及列表
    showMentionsList.value = false;

    // 设置新的光标位置
    nextTick(() => {
      const newCursorPosition = newTextBeforeCursor.length;
      textareaRef.value.focus();
      textareaRef.value.setSelectionRange(newCursorPosition, newCursorPosition);
      if (props.autoHeight) {
        adjustHeight();
      }
    });
  }
};

const handleKeyDown = event => {
  if (!showMentionsList.value) {
    return;
  }
  if (showMentionsList.value && filteredMentions.value.length > 0) {
    if (event.key === 'ArrowDown') {
      event.preventDefault();
      highlightedMentionIndex.value =
        (highlightedMentionIndex.value + 1) % filteredMentions.value.length;
    } else if (event.key === 'ArrowUp') {
      event.preventDefault();
      highlightedMentionIndex.value =
        (highlightedMentionIndex.value - 1 + filteredMentions.value.length) %
        filteredMentions.value.length;
    } else if (event.key === 'Enter') {
      event.preventDefault();
      selectMention(filteredMentions.value[highlightedMentionIndex.value]);
    } else if (event.key === 'Escape') {
      event.preventDefault();
      showMentionsList.value = false;
      textareaRef.value?.focus(); // 确保 textarea 保持焦点
    }
  }
};

const handleInput = event => {
  emit('update:modelValue', event.target.value);
  if (props.autoHeight) {
    nextTick(adjustHeight);
  }

  // 处理 @ 提及功能
  if (props.mentionOptions && props.mentionOptions.length > 0) {
    // Don't re-check for mention if we are just navigating the list or confirming
    if (!['ArrowDown', 'ArrowUp', 'Enter', 'Escape'].includes(event.key)) {
      checkForMention(event.target.value);
    }
  }
};

const handleFocus = () => {
  if (!props.disabled) {
    isFocused.value = true;
  }
};

const handleBlur = () => {
  if (!props.disabled) {
    isFocused.value = false;
  }
};

// Watch for changes in modelValue to adjust height
watch(
  () => props.modelValue,
  () => {
    if (props.autoHeight) {
      nextTick(adjustHeight);
    }
  }
);

watch(
  () => props.sendWithSignature,
  newValue => {
    if (props.allowSignature) toggleSignatureInEditor(newValue);
  }
);

onMounted(() => {
  if (props.autoHeight) {
    nextTick(adjustHeight);
  }

  if (props.autofocus) {
    textareaRef.value?.focus();
  }
});
</script>

<template>
  <div class="relative flex flex-col gap-1">
    <label
      v-if="label"
      :for="id"
      class="mb-0.5 text-sm font-medium text-n-slate-12"
    >
      {{ label }}
    </label>
    <div
      class="flex flex-col gap-2 px-3 pt-3 pb-3 transition-all duration-500 ease-in-out border rounded-lg bg-n-alpha-black2"
      :class="[
        customTextAreaWrapperClass,
        {
          'cursor-not-allowed opacity-50 !bg-n-alpha-black2 disabled:border-n-weak dark:disabled:border-n-weak':
            disabled,
          'border-n-brand dark:border-n-brand': isFocused,
          'hover:border-n-slate-6 dark:hover:border-n-slate-6 border-n-weak dark:border-n-weak':
            !isFocused && messageType !== 'error',
          'border-n-ruby-8 dark:border-n-ruby-8 hover:border-n-ruby-9 dark:hover:border-n-ruby-9':
            messageType === 'error' && !isFocused,
        },
      ]"
    >
      <slot /><!-- Slot for adding popover -->
      <textarea
        :id="id"
        ref="textareaRef"
        :value="modelValue"
        :placeholder="placeholder"
        :maxlength="showCharacterCount ? maxLength : undefined"
        :class="[
          customTextAreaClass,
          {
            'resize-none': !resize,
          },
        ]"
        :style="{
          minHeight: autoHeight ? minHeight : undefined,
          maxHeight: autoHeight ? maxHeight : undefined,
        }"
        :disabled="disabled"
        rows="1"
        class="flex w-full reset-base text-sm p-0 !rounded-none !bg-transparent dark:!bg-transparent !border-0 !outline-0 !mb-0 placeholder:text-n-slate-10 dark:placeholder:text-n-slate-10 text-n-slate-12 dark:text-n-slate-12 disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-slate-25 dark:disabled:bg-slate-900"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown="handleKeyDown"
      />
      <div
        v-if="showCharacterCount"
        class="flex items-center justify-end h-4 mt-1 bottom-3 ltr:right-3 rtl:left-3"
      >
        <span class="text-xs tabular-nums text-n-slate-10">
          {{ `${characterCount} / ${maxLength}` }}
        </span>
      </div>
      <!-- 提及选项弹窗 -->
      <div
        v-if="showMentionsList && filteredMentions.length > 0"
        class="absolute z-10 max-h-40 w-48 overflow-y-auto bg-white dark:bg-n-solid-2 shadow-lg rounded-lg border border-n-weak dark:border-n-weak"
        :style="{
          top: mentionPosition.top + 20 + 'px',
          left: mentionPosition.left + 'px',
        }"
      >
        <ul class="py-1">
          <li
            v-for="(mention, index) in filteredMentions"
            :key="mention"
            class="px-3 py-2 cursor-pointer text-sm text-n-slate-12 dark:text-n-slate-12"
            :class="[
              index === highlightedMentionIndex
                ? 'bg-n-alpha-black2 dark:bg-n-slate-5'
                : 'hover:bg-n-alpha-black2 dark:hover:bg-n-slate-5',
            ]"
            @click="selectMention(mention)"
          >
            {{ `@${mention}` }}
          </li>
        </ul>
      </div>
    </div>
    <p
      v-if="message"
      class="min-w-0 mt-1 mb-0 text-xs truncate transition-all duration-500 ease-in-out"
      :class="messageClass"
    >
      {{ message }}
    </p>
  </div>
</template>
