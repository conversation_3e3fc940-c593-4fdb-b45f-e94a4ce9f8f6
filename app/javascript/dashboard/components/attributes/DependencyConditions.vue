<script>
import { mapGetters } from 'vuex';
import NextButton from 'dashboard/components-next/button/Button.vue';

export default {
  name: 'DependencyConditions',
  components: {
    NextButton,
  },
  props: {
    modelValue: {
      type: Object,
      default: () => ({
        enable: false,
        conditions: [],
      }),
    },
    attributeModel: {
      type: [String, Number],
      required: true,
    },
    currentAttributeId: {
      type: Number,
      default: null,
    },
  },
  emits: ['update:modelValue'],
  data() {
    return {
      localDependency: {
        enable: false,
        conditions: [],
      },
    };
  },
  computed: {
    ...mapGetters({
      getAttributesByModel: 'attributes/getAttributesByModel',
    }),
    availableAttributes() {
      // 将数字 ID 转换为字符串格式
      const modelMap = {
        0: 'conversation_attribute',
        1: 'contact_attribute',
      };
      const modelString =
        typeof this.attributeModel === 'number'
          ? modelMap[this.attributeModel]
          : this.attributeModel;

      const attributes = this.getAttributesByModel(modelString) || [];

      // 如果有当前属性ID，则排除自己
      return this.currentAttributeId
        ? attributes.filter(attr => attr.id !== this.currentAttributeId)
        : attributes;
    },
    isConditionsValid() {
      if (
        !this.localDependency.enable ||
        this.localDependency.conditions.length === 0
      ) {
        return true;
      }
      return this.localDependency.conditions.every(
        condition =>
          condition.attributeKey &&
          condition.operator &&
          condition.value !== '' &&
          condition.value !== null &&
          condition.value !== undefined
      );
    },
  },
  watch: {
    modelValue: {
      handler(newVal) {
        if (newVal) {
          // Vue 3 中直接赋值即可，不需要 $set
          this.localDependency = {
            enable: newVal.enable || false,
            conditions: newVal.conditions ? [...newVal.conditions] : [],
          };
        }
      },
      immediate: true,
      deep: true,
    },
    localDependency: {
      handler(newVal) {
        // 避免无限循环，只在值真正改变时才触发
        const currentValue = this.modelValue || {};
        if (
          newVal.enable !== currentValue.enable ||
          JSON.stringify(newVal.conditions) !==
            JSON.stringify(currentValue.conditions || [])
        ) {
          this.$emit('update:modelValue', {
            enable: newVal.enable,
            conditions: [...newVal.conditions],
          });
        }
      },
      deep: true,
    },
  },
  methods: {
    toggleDependOnEnabled() {
      // this.localDependency.enable = !this.localDependency.enable;
      if (!this.localDependency.enable) {
        this.localDependency.conditions = [];
      } else if (this.localDependency.conditions.length === 0) {
        this.addDependencyCondition();
      }
    },
    addDependencyCondition() {
      const newCondition = {
        attributeKey: '',
        operator: 'equal_to',
        value: '',
        logicalOperator: 'and', // and/or
      };
      this.localDependency.conditions.push(newCondition);
    },
    removeDependencyCondition(index) {
      if (this.localDependency.conditions.length === 1) {
        this.localDependency.conditions = [];
        this.localDependency.enable = false;
      } else {
        this.localDependency.conditions.splice(index, 1);
      }
    },
    onDependencyAttributeChange(index) {
      // 清空依赖值当属性改变时 - Vue 3 中直接赋值即可
      this.localDependency.conditions[index] = {
        ...this.localDependency.conditions[index],
        value: '',
      };
    },
    getSelectedDependOnAttribute(attributeKey) {
      return this.availableAttributes.find(
        attr => attr.attribute_key === attributeKey
      );
    },
    getDependOnAttributeOptions(attributeKey) {
      const selectedAttr = this.getSelectedDependOnAttribute(attributeKey);
      if (
        selectedAttr?.attribute_display_type === 'list' &&
        selectedAttr?.attribute_values
      ) {
        return selectedAttr.attribute_values.map(value => ({
          name: value,
          value: value,
        }));
      }
      return [];
    },
    // 获取操作符选项，根据属性类型返回不同的操作符
    getOperatorOptions(attributeType) {
      const baseOperators = [
        {
          value: 'equal_to',
          label: this.$t('ATTRIBUTES_MGMT.ADD.FORM.OPERATORS.EQUAL_TO'),
        },
        {
          value: 'not_equal_to',
          label: this.$t('ATTRIBUTES_MGMT.ADD.FORM.OPERATORS.NOT_EQUAL_TO'),
        },
      ];

      if (attributeType === 'text') {
        // text
        return [
          ...baseOperators,
          {
            value: 'contains',
            label: this.$t('ATTRIBUTES_MGMT.ADD.FORM.OPERATORS.CONTAINS'),
          },
          {
            value: 'not_contains',
            label: this.$t('ATTRIBUTES_MGMT.ADD.FORM.OPERATORS.NOT_CONTAINS'),
          },
          {
            value: 'starts_with',
            label: this.$t('ATTRIBUTES_MGMT.ADD.FORM.OPERATORS.STARTS_WITH'),
          },
          {
            value: 'ends_with',
            label: this.$t('ATTRIBUTES_MGMT.ADD.FORM.OPERATORS.ENDS_WITH'),
          },
          {
            value: 'regex_match',
            label: this.$t('ATTRIBUTES_MGMT.ADD.FORM.OPERATORS.REGEX_MATCH'),
          },
        ];
      }
      if (attributeType === 'list') {
        // list 类型只需要基础的等于和不等于操作符
        return baseOperators;
      }
      if (attributeType === 'number' || attributeType === 'date') {
        // number or date
        return [
          ...baseOperators,
          {
            value: 'greater_than',
            label: this.$t('ATTRIBUTES_MGMT.ADD.FORM.OPERATORS.GREATER_THAN'),
          },
          {
            value: 'less_than',
            label: this.$t('ATTRIBUTES_MGMT.ADD.FORM.OPERATORS.LESS_THAN'),
          },
          {
            value: 'greater_than_or_equal',
            label: this.$t(
              'ATTRIBUTES_MGMT.ADD.FORM.OPERATORS.GREATER_THAN_OR_EQUAL'
            ),
          },
          {
            value: 'less_than_or_equal',
            label: this.$t(
              'ATTRIBUTES_MGMT.ADD.FORM.OPERATORS.LESS_THAN_OR_EQUAL'
            ),
          },
        ];
      }
      return baseOperators;
    },
  },
};
</script>

<template>
  <div class="mt-6">
    <div class="flex items-center gap-1 font-medium text-n-slate-12">
      <input
        v-model="localDependency.enable"
        type="checkbox"
        @change="toggleDependOnEnabled"
      />
      {{ ' ' + $t('ATTRIBUTES_MGMT.ADD.FORM.ENABLE_DEPENDENCY.LABEL') }}
    </div>

    <!-- 多条件依赖配置 -->
    <div
      v-if="localDependency.enable"
      class="mt-4 p-4 bg-n-alpha-2 border border-n-weak rounded-lg"
    >
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-sm font-semibold text-n-slate-12">
          {{ $t('ATTRIBUTES_MGMT.ADD.FORM.DEPENDENCY_CONFIG.TITLE') }}
        </h4>
        <NextButton
          xs
          slate
          faded
          icon="i-lucide-plus"
          :label="$t('ATTRIBUTES_MGMT.ADD.FORM.ADD_CONDITION')"
          @click.prevent="addDependencyCondition"
        />
      </div>

      <!-- 依赖条件列表 -->
      <div class="space-y-3">
        <div
          v-for="(condition, index) in localDependency.conditions"
          :key="index"
          class="relative"
        >
          <!-- 逻辑操作符 (AND/OR) - 除了第一个条件 -->
          <div v-if="index > 0" class="flex justify-center">
            <select
              v-model="condition.logicalOperator"
              class="text-xs font-medium border border-n-weak rounded-md bg-n-alpha-2 text-n-slate-11 focus:border-n-brand focus:ring-1 focus:ring-n-brand focus:outline-none !mb-3"
            >
              <option value="and">
                {{ $t('ATTRIBUTES_MGMT.ADD.FORM.LOGICAL_OPERATORS.AND') }}
              </option>
              <option value="or">
                {{ $t('ATTRIBUTES_MGMT.ADD.FORM.LOGICAL_OPERATORS.OR') }}
              </option>
            </select>
          </div>

          <!-- 条件配置卡片 -->
          <div
            class="flex items-center gap-3 p-3 bg-white dark:bg-n-solid-2 border border-n-weak rounded-lg"
          >
            <!-- 属性选择 -->
            <div class="flex-1 min-w-0">
              <label class="block text-xs font-medium text-n-slate-11 mb-1">
                {{ $t('ATTRIBUTES_MGMT.ADD.FORM.DEPEND_ON_ATTRIBUTE.LABEL') }}
              </label>
              <select
                v-model="condition.attributeKey"
                class="w-full text-sm border border-n-weak rounded-md bg-white dark:bg-n-solid-2 text-n-slate-12 focus:border-n-brand focus:ring-1 focus:ring-n-brand focus:outline-none"
                @change="onDependencyAttributeChange(index)"
              >
                <option value="">
                  {{
                    $t(
                      'ATTRIBUTES_MGMT.ADD.FORM.DEPEND_ON_ATTRIBUTE.PLACEHOLDER'
                    )
                  }}
                </option>
                <option
                  v-for="attr in availableAttributes"
                  :key="attr.attribute_key"
                  :value="attr.attribute_key"
                >
                  {{ attr.attribute_display_name }}
                </option>
              </select>
            </div>

            <!-- 操作符选择 -->
            <div class="flex-1 min-w-0">
              <label class="block text-xs font-medium text-n-slate-11 mb-1">
                {{ $t('ATTRIBUTES_MGMT.ADD.FORM.OPERATOR.LABEL') }}
              </label>
              <select
                v-model="condition.operator"
                class="w-full text-sm border border-n-weak rounded-md bg-white dark:bg-n-solid-2 text-n-slate-12 focus:border-n-brand focus:ring-1 focus:ring-n-brand focus:outline-none"
              >
                <option
                  v-for="op in getOperatorOptions(
                    getSelectedDependOnAttribute(condition.attributeKey)
                      ?.attribute_display_type || 'text'
                  )"
                  :key="op.value"
                  :value="op.value"
                >
                  {{ op.label }}
                </option>
              </select>
            </div>

            <!-- 值输入 -->
            <div class="flex-1 min-w-0">
              <label class="block text-xs font-medium text-n-slate-11 mb-1">
                {{ $t('ATTRIBUTES_MGMT.ADD.FORM.DEPEND_ON_VALUE.LABEL') }}
              </label>
              <!-- 列表类型：下拉选择 -->
              <select
                v-if="
                  getSelectedDependOnAttribute(condition.attributeKey)
                    ?.attribute_display_type === 'list'
                "
                v-model="condition.value"
                class="w-full text-sm border border-n-weak rounded-md bg-white dark:bg-n-solid-2 text-n-slate-12 focus:border-n-brand focus:ring-1 focus:ring-n-brand focus:outline-none"
              >
                <option value="">
                  {{
                    $t('ATTRIBUTES_MGMT.ADD.FORM.DEPEND_ON_VALUE.PLACEHOLDER')
                  }}
                </option>
                <option
                  v-for="option in getDependOnAttributeOptions(
                    condition.attributeKey
                  )"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.name }}
                </option>
              </select>

              <!-- 复选框类型：YES/NO 选择 -->
              <select
                v-else-if="
                  getSelectedDependOnAttribute(condition.attributeKey)
                    ?.attribute_display_type === 'checkbox'
                "
                v-model="condition.value"
                class="w-full text-sm border border-n-weak rounded-md bg-white dark:bg-n-solid-2 text-n-slate-12 focus:border-n-brand focus:ring-1 focus:ring-n-brand focus:outline-none"
              >
                <option value="">
                  {{
                    $t('ATTRIBUTES_MGMT.ADD.FORM.DEPEND_ON_VALUE.PLACEHOLDER')
                  }}
                </option>
                <option value="0">
                  {{ $t('ATTRIBUTES_MGMT.ADD.FORM.CHECKBOX_OPTIONS.NO') }}
                </option>
                <option value="1">
                  {{ $t('ATTRIBUTES_MGMT.ADD.FORM.CHECKBOX_OPTIONS.YES') }}
                </option>
              </select>

              <!-- 日期类型 -->
              <input
                v-else-if="
                  getSelectedDependOnAttribute(condition.attributeKey)
                    ?.attribute_display_type === 'date'
                "
                v-model="condition.value"
                type="date"
                class="w-full px-3 py-2 text-sm border border-n-weak rounded-md bg-white dark:bg-n-solid-2 text-n-slate-12 focus:border-n-brand focus:ring-1 focus:ring-n-brand focus:outline-none"
              />

              <!-- 数字类型 -->
              <input
                v-else-if="
                  getSelectedDependOnAttribute(condition.attributeKey)
                    ?.attribute_display_type === 'number'
                "
                v-model="condition.value"
                type="number"
                class="w-full px-3 py-2 text-sm border border-n-weak rounded-md bg-white dark:bg-n-solid-2 text-n-slate-12 focus:border-n-brand focus:ring-1 focus:ring-n-brand focus:outline-none"
                :placeholder="
                  $t('ATTRIBUTES_MGMT.ADD.FORM.DEPEND_ON_VALUE.PLACEHOLDER')
                "
              />

              <!-- 文本类型和其他类型 -->
              <input
                v-else
                v-model="condition.value"
                type="text"
                class="w-full px-3 py-2 text-sm border border-n-weak rounded-md bg-white dark:bg-n-solid-2 text-n-slate-12 focus:border-n-brand focus:ring-1 focus:ring-n-brand focus:outline-none"
                :placeholder="
                  $t('ATTRIBUTES_MGMT.ADD.FORM.DEPEND_ON_VALUE.PLACEHOLDER')
                "
              />
            </div>

            <!-- 删除按钮 -->
            <div class="flex-shrink-0 pt-5">
              <NextButton
                xs
                slate
                ghost
                icon="i-lucide-trash-2"
                @click.prevent="removeDependencyCondition(index)"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 条件验证提示 -->
      <div
        v-if="localDependency.enable && !isConditionsValid"
        class="mt-3 p-3 bg-n-ruby-2 border border-n-ruby-6 rounded-md"
      >
        <div class="flex items-center gap-2">
          <i class="i-lucide-alert-circle text-n-ruby-9 text-sm" />
          <span class="text-sm text-n-ruby-11">
            {{ $t('ATTRIBUTES_MGMT.ADD.FORM.DEPENDENCY_VALIDATION_ERROR') }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
// 确保select元素的下拉箭头不被遮挡
// select {
//   background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' version='1.1' width='32' height='24' viewBox='0 0 32 24'><polygon points='0,0 32,0 16,24' style='fill: rgb%28110, 111, 115%29'></polygon></svg>");
//   background-size: 9px 6px;
//   background-repeat: no-repeat;
//   background-position: right 0.75rem center;
//   padding-right: 2rem !important;

//   &:dir(rtl) {
//     background-position: left 0.75rem center;
//     padding-left: 2rem !important;
//     padding-right: 0.75rem !important;
//   }
// }
</style>
