# DependencyConditions 组件

这是一个可复用的Vue组件，用于处理属性的依赖条件配置。

## 功能特性

- 支持多条件依赖配置
- 支持AND/OR逻辑操作符
- 根据属性类型动态显示不同的操作符选项
- 根据属性类型动态显示不同的值输入控件
- 支持文本、数字、日期、列表、复选框等属性类型
- 实时验证条件完整性

## 使用方法

```vue
<template>
  <DependencyConditions
    v-model="dependOnEnabled"
    v-model:conditions="dependencyConditions"
    :attribute-model="attributeModel"
    :current-attribute-id="currentAttributeId"
  />
</template>

<script>
import DependencyConditions from 'dashboard/components/attributes/DependencyConditions.vue';

export default {
  components: {
    DependencyConditions,
  },
  data() {
    return {
      dependOnEnabled: false,
      dependencyConditions: [],
      attributeModel: 0, // 0: conversation_attribute, 1: contact_attribute
      currentAttributeId: null, // 当前编辑的属性ID（编辑模式下使用）
    };
  },
};
</script>
```

## Props

| 属性名 | 类型 | 必需 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `modelValue` | Boolean | 否 | false | 是否启用依赖条件 |
| `conditions` | Array | 否 | [] | 依赖条件数组 |
| `attributeModel` | String/Number | 是 | - | 属性模型类型 |
| `currentAttributeId` | Number | 否 | null | 当前属性ID（编辑模式下排除自己） |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:modelValue` | Boolean | 依赖条件启用状态变化 |
| `update:conditions` | Array | 依赖条件数组变化 |

## 条件数据结构

```javascript
{
  attributeKey: 'string',      // 依赖的属性key
  operator: 'string',          // 操作符 (equal_to, not_equal_to, contains, etc.)
  value: 'string',             // 条件值
  logicalOperator: 'string'    // 逻辑操作符 (and, or)
}
```

## 支持的操作符

### 基础操作符（所有类型）
- `equal_to` - 等于
- `not_equal_to` - 不等于

### 文本类型额外操作符
- `contains` - 包含
- `not_contains` - 不包含
- `starts_with` - 开始于
- `ends_with` - 结束于
- `regex_match` - 正则匹配

### 列表类型额外操作符
- `contains_any` - 包含任意
- `not_contains_any` - 不包含任意

### 数字/日期类型额外操作符
- `greater_than` - 大于
- `less_than` - 小于
- `greater_than_or_equal` - 大于等于
- `less_than_or_equal` - 小于等于

## 注意事项

1. 组件会自动根据选择的属性类型显示相应的操作符选项
2. 组件会自动根据属性类型显示相应的值输入控件
3. 组件内部使用了Vuex store来获取可用属性列表
4. 在编辑模式下，会自动排除当前正在编辑的属性
5. 组件会实时验证条件的完整性并显示错误提示 