<script>
import { mapGetters } from 'vuex';
import MentionBox from '../mentions/MentionBox.vue';

export default {
  components: { MentionBox },
  props: {
    searchKey: {
      type: String,
      default: '',
    },
    conversationId: {
      type: [Number, String],
      required: true,
    },
  },
  emits: ['execute'],
  computed: {
    ...mapGetters({
      allMacros: 'macros/getMacros',
    }),
    items() {
      // 只显示私有和公共的宏
      let filteredMacros = this.allMacros.filter(
        macro =>
          macro.visibility === 'personal' || macro.visibility === 'global'
      );

      // 如果有搜索关键词，进行搜索过滤
      if (this.searchKey && this.searchKey.trim()) {
        const searchTerm = this.searchKey.toLowerCase().trim();
        filteredMacros = filteredMacros.filter(macro => {
          // 按宏名称搜索
          const nameMatch = macro.name.toLowerCase().includes(searchTerm);
          // 也可以按描述搜索（如果有的话）
          const descriptionMatch = macro.description
            ? macro.description.toLowerCase().includes(searchTerm)
            : false;

          return nameMatch || descriptionMatch;
        });
      }

      return filteredMacros.map(macro => ({
        id: macro.id,
        label: macro.name,
        key: macro.name,
        description: this.$t('MACROS.SHORTCUTS.TRIGGER_DESCRIPTION', {
          macroName: macro.name,
        }),
        macro: macro,
      }));
    },
  },
  watch: {
    searchKey() {
      this.fetchMacros();
    },
  },
  mounted() {
    this.fetchMacros();
  },
  methods: {
    fetchMacros() {
      this.$store.dispatch('macros/get');
    },
    async handleMacroClick(item = {}) {
      try {
        // 立即执行宏
        await this.$store.dispatch('macros/execute', {
          macroId: item.macro.id,
          conversationIds: [this.conversationId],
        });

        // 通知父组件宏已执行，需要删除输入框中的 "#"
        this.$emit('execute', item.macro);

        // // 显示成功消息
        // this.$store.dispatch('alerts/show', {
        //   message: this.$t('MACROS.SHORTCUTS.EXECUTION_SUCCESS', {
        //     macroName: item.macro.name,
        //   }),
        //   type: 'success',
        // });
      } catch (error) {
        // // 显示错误消息
        // this.$store.dispatch('alerts/show', {
        //   message: this.$t('MACROS.SHORTCUTS.EXECUTION_ERROR'),
        //   type: 'error',
        // });
      }
    },
  },
};
</script>

<!-- eslint-disable-next-line vue/no-root-v-if -->
<template>
  <!-- v-if="items.length" -->
  <MentionBox :items="items" type="macro" @mention-select="handleMacroClick" />
</template>
