{"INTEGRATION_SETTINGS": {"SHOPIFY": {"DELETE": {"TITLE": "Delete Shopify Integration", "MESSAGE": "Are you sure you want to delete the Shopify integration?"}, "STORE_URL": {"TITLE": "Connect Shopify Store", "LABEL": "Store URL", "PLACEHOLDER": "your-store.myshopify.com", "HELP": "Enter your Shopify store's myshopify.com URL", "CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "SUBMIT": "Connect Store"}, "ERROR": "There was an error connecting to Shopify. Please try again or contact support if the issue persists."}, "HEADER": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DESCRIPTION": "AI Agent-OK integrates with multiple tools and services to improve your team's efficiency. Explore the list below to configure your favorite apps.", "LEARN_MORE": "Learn more about integrations", "LOADING": "Fetching integrations", "CAPTAIN": {"DISABLED": "Captain is not enabled on your account.", "CLICK_HERE_TO_CONFIGURE": "Click here to configure", "LOADING_CONSOLE": "Loading Captain <PERSON><PERSON><PERSON>...", "FAILED_TO_LOAD_CONSOLE": "Failed to load Captain <PERSON><PERSON>. Please refresh and try again."}, "WEBHOOK": {"SUBSCRIBED_EVENTS": "Evenimente subscrise", "LEARN_MORE": "Learn more about webhooks", "FORM": {"CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "DESC": "Evenimentele Webhook vă oferă informații în timp real despre ce se întâmplă în contul dvs. AI Agent-OK. Introduceți un URL valid pentru a configura un callback.", "SUBSCRIPTIONS": {"LABEL": "Evenimente", "EVENTS": {"CONVERSATION_CREATED": "Conversaț<PERSON> creată", "CONVERSATION_STATUS_CHANGED": "Stare conversație schimbată", "CONVERSATION_UPDATED": "Conversație actualizată", "MESSAGE_CREATED": "<PERSON><PERSON> creat", "MESSAGE_UPDATED": "<PERSON><PERSON>", "WEBWIDGET_TRIGGERED": "Widget de chat live deschis de utilizator", "CONTACT_CREATED": "Persoană de contact creată", "CONTACT_UPDATED": "Persoană de contact actualizată"}}, "END_POINT": {"LABEL": "URL Webhook", "PLACEHOLDER": "Example: {webhookExampleURL}", "ERROR": "Te rog introdu un URL valid"}, "EDIT_SUBMIT": "Actualizați webhook", "ADD_SUBMIT": "Creează webhook"}, "TITLE": "Webhook", "CONFIGURE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HEADER": "<PERSON><PERSON><PERSON>", "HEADER_BTN_TXT": "Adaugă un nou webhook", "LOADING": "<PERSON><PERSON><PERSON> webhook-<PERSON>ri <PERSON>", "SEARCH_404": "Nu există elemente care să corespundă acestei interogări", "SIDEBAR_TXT": "<p><b>Webhooks</b> </p> <p>Webhooks sunt request-uri HTTP care pot fi definite pentru fiecare cont. Acestea sunt declanșate de evenimente precum crearea de mesaje în AI Agent-OK. Poți crea mai mult de un singur webhook pentru acest cont. <br /><br /> Pentru a crea un webhook <b></b>, apasă pe butonul <b>Adaugă un nou webhook</b>. <PERSON> <PERSON><PERSON><PERSON>, puteți elimina orice webhook existent făcând clic pe butonul Ștergere.</p>", "LIST": {"404": "Nu există webhook-uri configurate pentru acest cont.", "TITLE": "Gestionează webhook-uri", "TABLE_HEADER": {"WEBHOOK_ENDPOINT": "Webhook endpoint", "ACTIONS": "Actiuni"}}, "EDIT": {"BUTTON_TEXT": "Editare", "TITLE": "Editare webhook", "API": {"SUCCESS_MESSAGE": "Configurația Webhook actualizată cu succes", "ERROR_MESSAGE": "Nu s-a putut conecta la Woot Server, încercați din nou mai târziu"}}, "ADD": {"CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "TITLE": "Adaugă un nou webhook", "API": {"SUCCESS_MESSAGE": "Configurare Webhook adăugată cu succes", "ERROR_MESSAGE": "Nu s-a putut conecta la Woot Server, încercați din nou mai târziu"}}, "DELETE": {"BUTTON_TEXT": "Şterge", "API": {"SUCCESS_MESSAGE": "Webhook șters cu succes", "ERROR_MESSAGE": "Nu s-a putut conecta la Woot Server, încercați din nou mai târziu"}, "CONFIRM": {"TITLE": "Confirmă ș<PERSON>ea", "MESSAGE": "Sunteți sigur de a șterge webhook? ({webhookURL})", "YES": "Da, șterge ", "NO": "Nu, păstreaza"}}}, "SLACK": {"DELETE": "Şterge", "DELETE_CONFIRMATION": {"TITLE": "Delete the integration", "MESSAGE": "Are you sure you want to delete the integration? Doing so will result in the loss of access to conversations on your Slack workspace."}, "HELP_TEXT": {"TITLE": "Utilizarea integră<PERSON>", "BODY": "With this integration, all of your incoming conversations will be synced to the ***{selectedChannel<PERSON><PERSON>}*** channel in your Slack workspace. You can manage all your customer conversations right within the channel and never miss a message.\n\nHere are the main features of the integration:\n\n**Respond to conversations from within Slack:** To respond to a conversation in the ***{selectedChannelName}*** Slack channel, simply type out your message and send it as a thread. This will create a response back to the customer through AI Agent-OK. It's that simple!\n\n **Create private notes:** If you want to create private notes instead of replies, start your message with ***`note:`***. This ensures that your message is kept private and won't be visible to the customer.\n\n**Associate an agent profile:** If the person who replied on Slack has an agent profile in AI Agent-OK under the same email, the replies will be associated with that agent profile automatically. This means you can easily track who said what and when. On the other hand, when the replier doesn't have an associated agent profile, the replies will appear from the bot profile to the customer.", "SELECTED": "selected"}, "SELECT_CHANNEL": {"OPTION_LABEL": "Select a channel", "UPDATE": "Actualizare", "BUTTON_TEXT": "Connect channel", "DESCRIPTION": "Your Slack workspace is now linked with AI Agent-OK. However, the integration is currently inactive. To activate the integration and connect a channel to AI Agent-OK, please click the button below.\n\n**Note:** If you are attempting to connect a private channel, add the AI Agent-OK app to the Slack channel before proceeding with this step.", "ATTENTION_REQUIRED": "Attention required", "EXPIRED": "Your Slack integration has expired. To continue receiving messages on Slack, please delete the integration and connect your workspace again."}, "UPDATE_ERROR": "There was an error updating the integration, please try again", "UPDATE_SUCCESS": "The channel is connected successfully", "FAILED_TO_FETCH_CHANNELS": "There was an error fetching the channels from Slack, please try again"}, "DYTE": {"CLICK_HERE_TO_JOIN": "Click aici pentru a vă alătura", "LEAVE_THE_ROOM": "Păr<PERSON><PERSON><PERSON><PERSON> camera", "START_VIDEO_CALL_HELP_TEXT": "Inițierea unui nou apel video cu clientul", "JOIN_ERROR": "A existat o eroare la aderarea la apel, vă rugăm să încercați din nou", "CREATE_ERROR": "A existat o eroare la crearea unui link de întâlnire, încercați din nou"}, "OPEN_AI": {"AI_ASSIST": "AI Assist", "WITH_AI": " {option} with AI ", "OPTIONS": {"REPLY_SUGGESTION": "Reply Suggestion", "SUMMARIZE": "Summarize", "REPHRASE": "Improve Writing", "FIX_SPELLING_GRAMMAR": "Fix Spelling and Grammar", "SHORTEN": "<PERSON>en", "EXPAND": "Expand", "MAKE_FRIENDLY": "Change message tone to friendly", "MAKE_FORMAL": "Use formal tone", "SIMPLIFY": "Simplify"}, "ASSISTANCE_MODAL": {"DRAFT_TITLE": "Draft content", "GENERATED_TITLE": "Generated content", "AI_WRITING": "AI is writing", "BUTTONS": {"APPLY": "Use this suggestion", "CANCEL": "<PERSON><PERSON><PERSON><PERSON>"}}, "CTA_MODAL": {"TITLE": "Integrate with OpenAI", "DESC": "Bring advanced AI features to your dashboard with OpenAI's GPT models. To begin, enter the API key from your OpenAI account.", "KEY_PLACEHOLDER": "Enter your OpenAI API key", "BUTTONS": {"NEED_HELP": "Aveţi nevoie de ajutor?", "DISMISS": "<PERSON><PERSON><PERSON>", "FINISH": "Finish Setup"}, "DISMISS_MESSAGE": "You can setup OpenAI integration later Whenever you want.", "SUCCESS_MESSAGE": "OpenAI integration setup successfully"}, "TITLE": "Îmbunătățiți cu IA", "SUMMARY_TITLE": "<PERSON><PERSON> cu <PERSON>", "REPLY_TITLE": "Sugestie de răspuns cu AI", "SUBTITLE": "Un răspuns îmbunătățit va fi generat folosind IA, pe baza proiectului curent.", "TONE": {"TITLE": "Ton", "OPTIONS": {"PROFESSIONAL": "Profesională", "FRIENDLY": "Amical"}}, "BUTTONS": {"GENERATE": "Genereaza", "GENERATING": "<PERSON><PERSON>…", "CANCEL": "<PERSON><PERSON><PERSON><PERSON>"}, "GENERATE_ERROR": "A existat o eroare de procesare a conținutului, vă rugăm să încercați din nou"}, "DELETE": {"BUTTON_TEXT": "Şterge", "API": {"SUCCESS_MESSAGE": "Integrare ștearsă cu succes"}}, "CONNECT": {"BUTTON_TEXT": "Conectează-te"}, "DASHBOARD_APPS": {"TITLE": "Aplicații tablou de bord", "HEADER_BTN_TXT": "Adăugarea unei noi aplicații pentru tabloul de bord", "SIDEBAR_TXT": "<p><b>Aplicații tablou de bord</b></p><p>Aplicațiile tablou de bord permit organizațiilor să încorporeze o aplicație în tabloul de bord AI Agent-OK pentru a oferi contextul pentru agenții de asistență pentru clienți. Această caracteristică vă permite să creați o aplicație independent și să o încorporați în interiorul tabloului de bord pentru a furniza informații despre utilizator, comenzile lor sau istoricul plăților anterioare.</p><p>Când încorporați aplicația utilizând tabloul de bord în AI Agent-OK, aplicația va primi contextul conversației și al contactului ca eveniment de fereastră. Implementați un ascultător pentru evenimentul mesajului de pe pagina dvs., pentru a primi contextul.</p><p>Pentru a adăuga o nouă aplicație de tablou de bord, faceți clic pe butonul \"Adăugați o nouă aplicație de tablou de bord\".</p>", "DESCRIPTION": "Aplicațiile tablou de bord permit organizațiilor să încorporeze o aplicație în interiorul tabloului de bord pentru a oferi contextul agenților de asistență pentru clienți. Această caracteristică vă permite să creați o aplicație independent și să o încorporați pentru a furniza informații despre utilizator, comenzile lor sau istoricul plăților anterioare.", "LEARN_MORE": "Learn more about Dashboard Apps", "LIST": {"404": "Nu există încă aplicații de tablou de bord configurate pe acest cont", "LOADING": "Preluarea aplicațiilor de tablou de bord...", "TABLE_HEADER": {"NAME": "Nume", "ENDPOINT": "Punct final"}, "EDIT_TOOLTIP": "Editarea aplicației", "DELETE_TOOLTIP": "Ștergerea aplicației"}, "FORM": {"TITLE_LABEL": "Nume", "TITLE_PLACEHOLDER": "Introduceți un nume pentru aplicația tablou de bord", "TITLE_ERROR": "Este necesar un nume pentru aplicația tabloul de bord", "URL_LABEL": "Punct final", "URL_PLACEHOLDER": "Introduceți adresa URL a punctului final în care este găzduită aplicația dvs", "URL_ERROR": "Este necesară o adresă URL validă"}, "CREATE": {"HEADER": "Adăugarea unei noi aplicații pentru tabloul de bord", "FORM_SUBMIT": "Trimite", "FORM_CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "API_SUCCESS": "Aplicația Tablou de bord configurată cu succes", "API_ERROR": "Nu am putut crea o aplicație. Încercați din nou mai târziu"}, "UPDATE": {"HEADER": "Accesați Tabloul de bord conversație", "FORM_SUBMIT": "Actualizare", "FORM_CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "API_SUCCESS": "Setările IMAP actualizate cu succes", "API_ERROR": "Nu am putut actualiza aplicația. Încercați din nou mai târziu"}, "DELETE": {"CONFIRM_YES": "<PERSON>, ștergeți acest lucru", "CONFIRM_NO": "Nu, păstreaza", "TITLE": "Confirmă ș<PERSON>ea", "MESSAGE": "Sunteți sigur că ștergeți aplicația - {appName}?", "API_SUCCESS": "Aplicația Tablou de bord ștearsă cu succes", "API_ERROR": "Nu am putut șterge aplicația. Încercați din nou mai târziu"}}, "LINEAR": {"ADD_OR_LINK_BUTTON": "Create/Link Linear Issue", "LOADING": "Fetching linear issues...", "LOADING_ERROR": "There was an error fetching the linear issues, please try again", "CREATE": "Creeaza", "LINK": {"SEARCH": "Search issues", "SELECT": "Select issue", "TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EMPTY_LIST": "No linear issues found", "LOADING": "Loading", "ERROR": "There was an error fetching the linear issues, please try again", "LINK_SUCCESS": "Issue linked successfully", "LINK_ERROR": "There was an error linking the issue, please try again", "LINK_TITLE": "Conversation (#{conversationId}) with {name}"}, "ADD_OR_LINK": {"TITLE": "Create/link linear issue", "DESCRIPTION": "Create Linear issues from conversations, or link existing ones for seamless tracking.", "FORM": {"TITLE": {"LABEL": "Titlu", "PLACEHOLDER": "Enter title", "REQUIRED_ERROR": "Titlul este necesar"}, "DESCRIPTION": {"LABEL": "Des<PERSON><PERSON><PERSON>", "PLACEHOLDER": "Enter description"}, "TEAM": {"LABEL": "Echipa", "PLACEHOLDER": "Selectați echipa", "SEARCH": "Search team", "REQUIRED_ERROR": "Team is required"}, "ASSIGNEE": {"LABEL": "Assignee", "PLACEHOLDER": "Select assignee", "SEARCH": "Search assignee"}, "PRIORITY": {"LABEL": "Prioritate", "PLACEHOLDER": "Selectați prioritatea", "SEARCH": "Search priority"}, "LABEL": {"LABEL": "Etichetă", "PLACEHOLDER": "Select label", "SEARCH": "Search label"}, "STATUS": {"LABEL": "Status", "PLACEHOLDER": "Select status", "SEARCH": "Search status"}, "PROJECT": {"LABEL": "Project", "PLACEHOLDER": "Select project", "SEARCH": "Search project"}}, "CREATE": "Creeaza", "CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "CREATE_SUCCESS": "Issue created successfully", "CREATE_ERROR": "There was an error creating the issue, please try again", "LOADING_TEAM_ERROR": "There was an error fetching the teams, please try again", "LOADING_TEAM_ENTITIES_ERROR": "There was an error fetching the team entities, please try again"}, "ISSUE": {"STATUS": "Status", "PRIORITY": "Prioritate", "ASSIGNEE": "Assignee", "LABELS": "Etichete", "CREATED_AT": "Created at {createdAt}"}, "UNLINK": {"TITLE": "Unlink", "SUCCESS": "Issue unlinked successfully", "ERROR": "There was an error unlinking the issue, please try again"}, "DELETE": {"TITLE": "Are you sure you want to delete the integration?", "MESSAGE": "Are you sure you want to delete the integration?", "CONFIRM": "Yes, delete", "CANCEL": "<PERSON><PERSON><PERSON><PERSON>"}}}, "CAPTAIN": {"NAME": "Captain", "HEADER_KNOW_MORE": "Know more", "COPILOT": {"SEND_MESSAGE": "Trimite mesaj...", "LOADER": "Captain is thinking", "YOU": "You", "USE": "Use this", "RESET": "Reset", "SELECT_ASSISTANT": "Select Assistant"}, "PAYWALL": {"TITLE": "Upgrade to use Captain AI", "AVAILABLE_ON": "Captain is not available on the free plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "UPGRADE_NOW": "Upgrade now", "CANCEL_ANYTIME": "You can change or cancel your plan anytime"}, "ENTERPRISE_PAYWALL": {"AVAILABLE_ON": "Captain AI feature is only available in a paid plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "ASK_ADMIN": "Please reach out to your administrator for the upgrade."}, "BANNER": {"RESPONSES": "You've used over 80% of your response limit. To continue using Captain AI, please upgrade.", "DOCUMENTS": "Document limit reached. Upgrade to continue using Captain AI."}, "FORM": {"CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "CREATE": "Creeaza", "EDIT": "Actualizare"}, "ASSISTANTS": {"HEADER": "Assistants", "ADD_NEW": "Create a new assistant", "DELETE": {"TITLE": "Are you sure to delete the assistant?", "DESCRIPTION": "This action is permanent. Deleting this assistant will remove it from all connected inboxes and permanently erase all generated knowledge.", "CONFIRM": "Yes, delete", "SUCCESS_MESSAGE": "The assistant has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the assistant, please try again."}, "FORM_DESCRIPTION": "Fill out the details below to name your assistant, describe its purpose, and specify the product it will support.", "CREATE": {"TITLE": "Create an assistant", "SUCCESS_MESSAGE": "The assistant has been successfully created", "ERROR_MESSAGE": "There was an error creating the assistant, please try again."}, "FORM": {"NAME": {"LABEL": "Assistant Name", "PLACEHOLDER": "Enter a name for the assistant", "ERROR": "Please provide a name for the assistant"}, "DESCRIPTION": {"LABEL": "Assistant Description", "PLACEHOLDER": "Describe how and where this assistant will be used", "ERROR": "A description is required"}, "PRODUCT_NAME": {"LABEL": "Product Name", "PLACEHOLDER": "Enter the name of the product this assistant is designed for", "ERROR": "The product name is required"}, "FEATURES": {"TITLE": "Caracteristici", "ALLOW_CONVERSATION_FAQS": "Generate FAQs from resolved conversations", "ALLOW_MEMORIES": "Capture key details as memories from customer interactions."}}, "EDIT": {"TITLE": "Update the assistant", "SUCCESS_MESSAGE": "The assistant has been successfully updated", "ERROR_MESSAGE": "There was an error updating the assistant, please try again."}, "OPTIONS": {"EDIT_ASSISTANT": "Edit Assistant", "DELETE_ASSISTANT": "Delete Assistant", "VIEW_CONNECTED_INBOXES": "View connected inboxes"}, "EMPTY_STATE": {"TITLE": "No assistants available", "SUBTITLE": "Create an assistant to provide quick and accurate responses to your users. It can learn from your help articles and past conversations.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain Assistant", "NOTE": "Captain Assistant engages directly with customers, learns from your help docs and past conversations, and delivers instant, accurate responses. It handles the initial queries, providing quick resolutions before  transferring to an agent when needed."}}}, "DOCUMENTS": {"HEADER": "Documents", "ADD_NEW": "Create a new document", "RELATED_RESPONSES": {"TITLE": "Related FAQs", "DESCRIPTION": "These FAQs are generated directly from the document."}, "FORM_DESCRIPTION": "Enter the URL of the document to add it as a knowledge source and choose the assistant to associate it with.", "CREATE": {"TITLE": "Add a document", "SUCCESS_MESSAGE": "The document has been successfully created", "ERROR_MESSAGE": "There was an error creating the document, please try again."}, "FORM": {"URL": {"LABEL": "URL", "PLACEHOLDER": "Enter the URL of the document", "ERROR": "Please provide a valid URL for the document"}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select the assistant", "ERROR": "The assistant field is required"}}, "DELETE": {"TITLE": "Are you sure to delete the document?", "DESCRIPTION": "This action is permanent. Deleting this document will permanently erase all generated knowledge.", "CONFIRM": "Yes, delete", "SUCCESS_MESSAGE": "The document has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the document, please try again."}, "OPTIONS": {"VIEW_RELATED_RESPONSES": "View Related Responses", "DELETE_DOCUMENT": "Delete Document"}, "EMPTY_STATE": {"TITLE": "No documents available", "SUBTITLE": "Documents are used by your assistant to generate FAQs. You can import documents to provide context for your assistant.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain <PERSON>ument", "NOTE": "A document in Captain serves as a knowledge resource for the assistant. By connecting your help center or guides, Captain can analyze the content and provide accurate responses for customer inquiries."}}}, "RESPONSES": {"HEADER": "FAQs", "ADD_NEW": "Create new FAQ", "DOCUMENTABLE": {"CONVERSATION": "Conversation #{id}"}, "SELECTED": "{count} selected", "BULK_APPROVE_BUTTON": "Approve", "BULK_DELETE_BUTTON": "Şterge", "BULK_APPROVE": {"SUCCESS_MESSAGE": "FAQs approved successfully", "ERROR_MESSAGE": "There was an error approving the FAQs, please try again."}, "BULK_DELETE": {"TITLE": "Delete FAQs?", "DESCRIPTION": "Are you sure you want to delete the selected FAQs? This action cannot be undone.", "CONFIRM": "Yes, delete all", "SUCCESS_MESSAGE": "FAQs deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQs, please try again."}, "DELETE": {"TITLE": "Are you sure to delete the FAQ?", "DESCRIPTION": "", "CONFIRM": "Yes, delete", "SUCCESS_MESSAGE": "FAQ deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQ, please try again."}, "FILTER": {"ASSISTANT": "Assistant: {selected}", "STATUS": "Status: {selected}", "ALL_ASSISTANTS": "Toate"}, "STATUS": {"TITLE": "Status", "PENDING": "În aşteptare", "APPROVED": "Approved", "ALL": "Toate"}, "FORM_DESCRIPTION": "Add a question and its corresponding answer to the knowledge base and select the assistant it should be associated with.", "CREATE": {"TITLE": "Add an FAQ", "SUCCESS_MESSAGE": "The response has been added successfully.", "ERROR_MESSAGE": "An error occurred while adding the response. Please try again."}, "FORM": {"QUESTION": {"LABEL": "Question", "PLACEHOLDER": "Enter the question here", "ERROR": "Please provide a valid question."}, "ANSWER": {"LABEL": "Answer", "PLACEHOLDER": "Enter the answer here", "ERROR": "Please provide a valid answer."}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select an assistant", "ERROR": "Please select an assistant."}}, "EDIT": {"TITLE": "Update the FAQ", "SUCCESS_MESSAGE": "The FAQ has been successfully updated", "ERROR_MESSAGE": "There was an error updating the FAQ, please try again", "APPROVE_SUCCESS_MESSAGE": "The FAQ was marked as approved"}, "OPTIONS": {"APPROVE": "Mark as approved", "EDIT_RESPONSE": "Edit FAQ", "DELETE_RESPONSE": "Delete FAQ"}, "EMPTY_STATE": {"TITLE": "No FAQs Found", "SUBTITLE": "FAQs help your assistant provide quick and accurate answers to questions from your customers. They can be generated automatically from your content or can be added manually.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain FAQ", "NOTE": "Captain <PERSON><PERSON><PERSON> detects common customer questions—whether missing from your knowledge base or frequently asked—and generates relevant FAQs to improve support. You can review each suggestion and decide whether to approve or reject it."}}}, "INBOXES": {"HEADER": "Connected Inboxes", "ADD_NEW": "Connect a new inbox", "OPTIONS": {"DISCONNECT": "Deconectare"}, "DELETE": {"TITLE": "Are you sure to disconnect the inbox?", "DESCRIPTION": "", "CONFIRM": "Yes, delete", "SUCCESS_MESSAGE": "The inbox was successfully disconnected.", "ERROR_MESSAGE": "There was an error disconnecting the inbox, please try again."}, "FORM_DESCRIPTION": "Choose an inbox to connect with the assistant.", "CREATE": {"TITLE": "Connect an Inbox", "SUCCESS_MESSAGE": "The inbox was successfully connected.", "ERROR_MESSAGE": "An error occurred while connecting the inbox. Please try again."}, "FORM": {"INBOX": {"LABEL": "Inbox", "PLACEHOLDER": "Choose the inbox to deploy the assistant.", "ERROR": "An inbox selection is required."}}, "EMPTY_STATE": {"TITLE": "No Connected Inboxes", "SUBTITLE": "Connecting an inbox allows the assistant to handle initial questions from your customers before transferring them to you."}}}}