{"AUDIT_LOGS": {"HEADER": "Audito Žurnalas", "HEADER_BTN_TXT": "<PERSON><PERSON><PERSON><PERSON>", "LOADING": "Parsiunčiami Audito Žurnalai", "DESCRIPTION": "Audit Logs maintain a record of activities in your account, allowing you to track and audit your account, team, or services.", "LEARN_MORE": "Learn more about audit logs", "SEARCH_404": "<PERSON><PERSON><PERSON> už<PERSON>ą atitinkančių elementų nėra", "SIDEBAR_TXT": "<p><b>Audito žurnalai</b> </p><p> Audito žurnalai yra įvykių ir veiksmų AI Agent-OK sistemoje pėdsakai. </p>", "LIST": {"404": "Šiai paskyrai nėra prieinamų Audito Žurnalų.", "TITLE": "<PERSON><PERSON><PERSON><PERSON> Audito <PERSON>", "DESC": "Audito žurnalai yra įvykių ir veiksmų „AI Agent-OK“ sistemoje pėdsakai.", "TABLE_HEADER": {"ACTIVITY": "Vartotojas", "TIME": "Action", "IP_ADDRESS": "IP Adresas"}}, "API": {"SUCCESS_MESSAGE": "Audito Žurnalai parsiųsti sėkmingai", "ERROR_MESSAGE": "Nepavyko prisijungti prie Woot serverio, bandykite dar kartą vėliau"}, "DEFAULT_USER": "Sistema", "AUTOMATION_RULE": {"ADD": "{agentName} created a new automation rule (#{id})", "EDIT": "{agentName} updated an automation rule (#{id})", "DELETE": "{agent<PERSON>ame} deleted an automation rule (#{id})"}, "ACCOUNT_USER": {"ADD": "{agentName} pak<PERSON><PERSON><PERSON> {invitee} į paskyrą kaip {role}", "EDIT": {"SELF": "{agentName} p<PERSON><PERSON><PERSON> savo {attributes} į {values}", "OTHER": "{agentName} pakeitė {attributes} iš {user} į {values}", "DELETED": "{agent<PERSON><PERSON>} changed {attributes} of a deleted user to {values}"}}, "INBOX": {"ADD": "{agentName} created a new inbox (#{id})", "EDIT": "{agentName} updated an inbox (#{id})", "DELETE": "{agent<PERSON>ame} deleted an inbox (#{id})"}, "WEBHOOK": {"ADD": "{agent<PERSON>ame} created a new webhook (#{id})", "EDIT": "{agent<PERSON>ame} updated a webhook (#{id})", "DELETE": "{agent<PERSON><PERSON>} deleted a webhook (#{id})"}, "USER_ACTION": {"SIGN_IN": "{agentName} prisijungęs", "SIGN_OUT": "{agentName} atsijungęs"}, "TEAM": {"ADD": "{<PERSON><PERSON><PERSON>} created a new team (#{id})", "EDIT": "{<PERSON><PERSON><PERSON>} updated a team (#{id})", "DELETE": "{<PERSON><PERSON><PERSON>} deleted a team (#{id})"}, "MACRO": {"ADD": "{agent<PERSON>ame} created a new macro (#{id})", "EDIT": "{agent<PERSON>ame} updated a macro (#{id})", "DELETE": "{agent<PERSON>ame} deleted a macro (#{id})"}, "INBOX_MEMBER": {"ADD": "{agent<PERSON>ame} added {user} to the inbox(#{inbox_id})", "REMOVE": "{<PERSON><PERSON><PERSON>} removed {user} from the inbox(#{inbox_id})"}, "TEAM_MEMBER": {"ADD": "{<PERSON><PERSON><PERSON>} added {user} to the team(#{team_id})", "REMOVE": "{<PERSON><PERSON><PERSON>} removed {user} from the team(#{team_id})"}, "ACCOUNT": {"EDIT": "{<PERSON><PERSON><PERSON>} updated the account configuration (#{id})"}}}