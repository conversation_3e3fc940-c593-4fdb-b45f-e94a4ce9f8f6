{"INBOX_MGMT": {"HEADER": "Gautų laiškų aplankai", "DESCRIPTION": "A channel is the mode of communication your customer chooses to interact with you. An inbox is where you manage interactions for a specific channel. It can include communications from various sources such as email, live chat, and social media.", "LEARN_MORE": "Learn more about inboxes", "RECONNECTION_REQUIRED": "Your inbox is disconnected. You won't receive new messages until you reauthorize it.", "CLICK_TO_RECONNECT": "Click here to reconnect.", "LIST": {"404": "Prie šios paskyros nėra pridėtų gautųjų laiškų aplankų."}, "CREATE_FLOW": {"CHANNEL": {"TITLE": "<PERSON><PERSON><PERSON><PERSON>", "BODY": "Pasirinkite paslaugų te<PERSON>, kurį norite integruoti su AI Agent-OK."}, "INBOX": {"TITLE": "Sukurti Gautų Laiškų Aplanką", "BODY": "Autentifikuokite savo paskyrą ir susikurkite gautų laiškų aplanką."}, "AGENT": {"TITLE": "<PERSON><PERSON><PERSON><PERSON>", "BODY": "Pridėti agentus pire gautų laiškų oplanko."}, "FINISH": {"TITLE": "Voilà!", "BODY": "Esate pasiruošę pradėti!"}}, "ADD": {"CHANNEL_NAME": {"LABEL": "Gautų Laiškų Aplanko Pavadinimas", "PLACEHOLDER": "Įveskite savo gautų laiškų aplanko pavadinimą (pvz.: Acme Inc)", "ERROR": "Prašome įrašyti teisingą gautų laiškų aplanko pavadinimą"}, "WEBSITE_NAME": {"LABEL": "Intenetinio <PERSON> pavadin<PERSON>s", "PLACEHOLDER": "Įveskite savo internetinio puslapio pavadinimą (pvz.: Acme Inc)"}, "FB": {"HELP": "PS: Po autorizacijos turėsime prieigą tik prie jūsų pranešimų puslapyje. AI Agent-OK asmeniniai pranešimai nebus pasiekiami.", "CHOOSE_PAGE": "Pa<PERSON>ink<PERSON> Puslapį", "CHOOSE_PLACEHOLDER": "Psirinkti puslapį iš s<PERSON>o", "INBOX_NAME": "Gautų Laiškų Aplanko Pavadinimas", "ADD_NAME": "Prašome įrašyti gautų laiškų aplanko pavadinimą", "PICK_NAME": "Pick a Name for your Inbox", "PICK_A_VALUE": "Pasirinkti re<PERSON>šmę"}, "TWITTER": {"HELP": "Norėdami pridėti savo Twitter profilį kaip kanal<PERSON>, turite patvir<PERSON>ti savo Twitter profilį spustelėdami „Prisijungti naudojant Twitter“ ", "ERROR_MESSAGE": "Prisijungiant prie Twitter įvyko klaida. Bandykite dar kartą", "TWEETS": {"ENABLE": "Kurkite pokalbius iš <PERSON> tweetų"}}, "WEBSITE_CHANNEL": {"TITLE": "Interneto svetainė<PERSON> kanal<PERSON>", "DESC": "Sukurkite kanalą savo svetainei ir pradėkite palaikyti savo klientus naudodami mūsų svetainės valdiklį.", "LOADING_MESSAGE": "Intenetinio puslapio palaikymo kanalo kū<PERSON>s", "CHANNEL_AVATAR": {"LABEL": "<PERSON><PERSON><PERSON>"}, "CHANNEL_WEBHOOK_URL": {"LABEL": "Webhook URL", "PLACEHOLDER": "Please enter your Webhook URL", "ERROR": "Prašome įvesti tesingą URL adresą"}, "CHANNEL_DOMAIN": {"LABEL": "Internetinio s<PERSON>", "PLACEHOLDER": "Įveskite savo internetinės svet<PERSON> p<PERSON> (pvz.: Acme Inc)"}, "CHANNEL_WELCOME_TITLE": {"LABEL": "Pasisveikinimo <PERSON>š<PERSON>ė", "PLACEHOLDER": "Sveiki!"}, "CHANNEL_WELCOME_TAGLINE": {"LABEL": "Pasisveikinimo tekstas", "PLACEHOLDER": "Su mumis susisiekti yra paprasta. Klauskite mūsų bet ko arba pasidalykite savo atsiliepimais."}, "CHANNEL_GREETING_MESSAGE": {"LABEL": "Kanalo pasisveikinimo pranešimas", "PLACEHOLDER": "Acme Inc paprastai atsako per kelias valandas."}, "CHANNEL_GREETING_TOGGLE": {"LABEL": "Leisti kanalo pasisveikinimą", "HELP_TEXT": "Automatically send a greeting message when a new conversation is created.", "ENABLED": "<PERSON><PERSON><PERSON>", "DISABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "REPLY_TIME": {"TITLE": "Nustatyti Atsakymo laiką", "IN_A_FEW_MINUTES": "Per kelias minutes", "IN_A_FEW_HOURS": "Per kelias minutes", "IN_A_DAY": "Per dien<PERSON>", "HELP_TEXT": "Šis atsakymo laikas bus rodomas tiesioginio pokalbio valdiklyje"}, "WIDGET_COLOR": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Atnaujinkite valdiklyje naudojamą valdiklio spalvą"}, "SUBMIT_BUTTON": "Sukurti Gautų Laiškų Aplanką", "API": {"ERROR_MESSAGE": "Nepavyko sukurti svet<PERSON> ka<PERSON>, bandykite dar kartą"}}, "TWILIO": {"TITLE": "Twilio SMS/Whatsapp Kanalas", "DESC": "Integruokite Twilio ir pradėkite komunikuoti su savo klientais SMS ar WhatsApp.", "ACCOUNT_SID": {"LABEL": "Paskyros SID", "PLACEHOLDER": "Įveskite <PERSON><PERSON><PERSON>", "ERROR": "<PERSON><PERSON> la<PERSON> yra privalomas"}, "API_KEY": {"USE_API_KEY": "Naudokite API Key autentifikavimą", "LABEL": "API Key SID", "PLACEHOLDER": "Įveskite savo API Key SID", "ERROR": "<PERSON><PERSON> la<PERSON> yra privalomas"}, "API_KEY_SECRET": {"LABEL": "API Key Secret", "PLACEHOLDER": "Įveskite savo API Key Secret", "ERROR": "<PERSON><PERSON> la<PERSON> yra privalomas"}, "MESSAGING_SERVICE_SID": {"LABEL": "Pranešimų siuntimo paslaugos SID", "PLACEHOLDER": "Įveskite jū<PERSON><PERSON> siuntimo paslaugos SID", "ERROR": "<PERSON><PERSON> la<PERSON> yra privalomas", "USE_MESSAGING_SERVICE": "Naudokite Twilio <PERSON> siuntimo paslaugą"}, "CHANNEL_TYPE": {"LABEL": "Kanalo tipas", "ERROR": "Pasirinkite savo Kanalo Tipą"}, "AUTH_TOKEN": {"LABEL": "P<PERSON><PERSON><PERSON>", "PLACEHOLDER": "Įveskite <PERSON><PERSON><PERSON> p<PERSON> p<PERSON>igo<PERSON> raktą", "ERROR": "<PERSON><PERSON> la<PERSON> yra privalomas"}, "CHANNEL_NAME": {"LABEL": "Gautų Laiškų Aplanko Pavadinimas", "PLACEHOLDER": "Prašome įrašyti gautų laiškų aplanko pavadinimą", "ERROR": "<PERSON><PERSON> la<PERSON> yra privalomas"}, "PHONE_NUMBER": {"LABEL": "Telefono numeris", "PLACEHOLDER": "Įveskite telefono numerį iš kurio bus siunčiamas pranešimas.", "ERROR": "Pateikite galiojantį telefono numerį, kuri<PERSON> pras<PERSON> „+“ ženklu ir kuriame nėra tarpų."}, "API_CALLBACK": {"TITLE": "Automatinio atskambinimo URL", "SUBTITLE": "Turite sukonfigūruoti pranešimo atgalinio skambinimo URL Twilio su čia nurodytu URL."}, "SUBMIT_BUTTON": "<PERSON><PERSON><PERSON><PERSON> T<PERSON> ka<PERSON>", "API": {"ERROR_MESSAGE": "Nepavyko autentifi<PERSON>, bandykite dar kartą"}}, "SMS": {"TITLE": "SMS kanalas", "DESC": "Pradėkite palaikyti savo klientus naudodami SMS.", "PROVIDERS": {"LABEL": "API tiekėjas", "TWILIO": "<PERSON><PERSON><PERSON>", "BANDWIDTH": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "API": {"ERROR_MESSAGE": "Mes negalė<PERSON> išsaugoti SMS kanalo"}, "BANDWIDTH": {"ACCOUNT_ID": {"LABEL": "Paskyros ID", "PLACEHOLDER": "Įveskite savo Bandwith Paskyros ID", "ERROR": "<PERSON><PERSON> la<PERSON> yra privalomas"}, "API_KEY": {"LABEL": "API raktas", "PLACEHOLDER": "Please enter your Bandwidth API Key", "ERROR": "<PERSON><PERSON> la<PERSON> yra privalomas"}, "API_SECRET": {"LABEL": "API Secret", "PLACEHOLDER": "Please enter your Bandwidth API Secret", "ERROR": "<PERSON><PERSON> la<PERSON> yra privalomas"}, "APPLICATION_ID": {"LABEL": "Programos ID", "PLACEHOLDER": "Įveskite savo Bandwith Programos ID", "ERROR": "<PERSON><PERSON> la<PERSON> yra privalomas"}, "INBOX_NAME": {"LABEL": "Gautų Laiškų Aplanko Pavadinimas", "PLACEHOLDER": "Prašome įrašyti gautų laiškų aplanko pavadinimą", "ERROR": "<PERSON><PERSON> la<PERSON> yra privalomas"}, "PHONE_NUMBER": {"LABEL": "Telefono numeris", "PLACEHOLDER": "Įveskite telefono numerį iš kurio bus siunčiamas pranešimas.", "ERROR": "Pateikite galiojantį telefono numerį, kuri<PERSON> pras<PERSON> „+“ ženklu ir kuriame nėra tarpų."}, "SUBMIT_BUTTON": "Sukurti Bandwidth Kanalą", "API": {"ERROR_MESSAGE": "Nepavyko autentifikuoti Bandwidth, bandykite dar kartą"}, "API_CALLBACK": {"TITLE": "Automatinio atskambinimo URL", "SUBTITLE": "Turite sukonfigūruoti pranešimo atgalinio skambinimo URL Bandwidth su čia nurodytu URL."}}}, "WHATSAPP": {"TITLE": "WhatsA<PERSON>", "DESC": "Pradėkite palaikyti savo klientus naudodami WhatsApp.", "PROVIDERS": {"LABEL": "API tiekėjas", "TWILIO": "<PERSON><PERSON><PERSON>", "WHATSAPP_CLOUD": "WhatsApp Cloud", "360_DIALOG": "360Dialog"}, "INBOX_NAME": {"LABEL": "Gautų Laiškų Aplanko Pavadinimas", "PLACEHOLDER": "Prašome įrašyti kanalo pavadinimą", "ERROR": "<PERSON><PERSON> la<PERSON> yra privalomas"}, "PHONE_NUMBER": {"LABEL": "Telefono numeris", "PLACEHOLDER": "Įveskite telefono numerį iš kurio bus siunčiamas pranešimas.", "ERROR": "Pateikite galiojantį telefono numerį, kuri<PERSON> pras<PERSON> „+“ ženklu ir kuriame nėra tarpų."}, "PHONE_NUMBER_ID": {"LABEL": "Telefono numerio ID", "PLACEHOLDER": "Įveskite telefono numerį, g<PERSON><PERSON> iš <PERSON> valdymo skydelio.", "ERROR": "Prašau įveskite teisingą reikš<PERSON>ę."}, "BUSINESS_ACCOUNT_ID": {"LABEL": "Įmonės paskyros ID", "PLACEHOLDER": "Įveskite Įmonės paskyros ID, gaut<PERSON> iš Facebook valdymo skydelio.", "ERROR": "Prašau įveskite teisingą reikš<PERSON>ę."}, "WEBHOOK_VERIFY_TOKEN": {"LABEL": "Webhook Tikrinti Prieigos Raktą", "PLACEHOLDER": "Enter a verify token which you want to configure for Facebook webhooks.", "ERROR": "Prašau įveskite teisingą reikš<PERSON>ę."}, "API_KEY": {"LABEL": "API raktas", "SUBTITLE": "Konfiguruoti WhatsApp API key.", "PLACEHOLDER": "API raktas", "ERROR": "Prašau įveskite teisingą reikš<PERSON>ę."}, "API_CALLBACK": {"TITLE": "Automatinio atskambinimo URL", "SUBTITLE": "Turite sukonfigūruoti webhook URL ir patvirtinimo prieigos raktą Facebook portale, naudodami toliau nurodytas reikšmes.", "WEBHOOK_URL": "Webhook URL", "WEBHOOK_VERIFICATION_TOKEN": "Webhook Patikros <PERSON>"}, "SUBMIT_BUTTON": "Sukurti WhatsApp Kanalą", "API": {"ERROR_MESSAGE": "Mes negalė<PERSON>ug<PERSON>i WhatsApp kanalo"}}, "API_CHANNEL": {"TITLE": "API kanalas", "DESC": "Integruokite API kanalą ir pradėkite komunikuoti su savo klientais.", "CHANNEL_NAME": {"LABEL": "Ka<PERSON><PERSON>", "PLACEHOLDER": "Prašome įrašyti kanalo pavadinimą", "ERROR": "<PERSON><PERSON> la<PERSON> yra privalomas"}, "WEBHOOK_URL": {"LABEL": "Webhook URL", "SUBTITLE": "Configure the URL where you want to receive callbacks on events.", "PLACEHOLDER": "Webhook URL"}, "SUBMIT_BUTTON": "Sukurti API kanalą", "API": {"ERROR_MESSAGE": "Mes negalėjome išsaugoti API kanalo"}}, "EMAIL_CHANNEL": {"TITLE": "El. p<PERSON><PERSON> ka<PERSON>", "DESC": "Integrate your email inbox.", "CHANNEL_NAME": {"LABEL": "Ka<PERSON><PERSON>", "PLACEHOLDER": "Prašome įrašyti kanalo pavadinimą", "ERROR": "<PERSON><PERSON> la<PERSON> yra privalomas"}, "EMAIL": {"LABEL": "El. <PERSON>", "SUBTITLE": "Email where your customers sends you support tickets", "PLACEHOLDER": "El. <PERSON>"}, "SUBMIT_BUTTON": "Sukurti El. pašto kanalą", "API": {"ERROR_MESSAGE": "Mes negalėjome išsaugoti el. pašto kanalo"}, "FINISH_MESSAGE": "Pradėkite peradresuoti savo el. laiškus toliau nurodytu el. pašto adresu."}, "LINE_CHANNEL": {"TITLE": "LINE kanalas", "DESC": "Integruokite LINE kanalą ir pradėkite komunikuoti su savo klientais.", "CHANNEL_NAME": {"LABEL": "Ka<PERSON><PERSON>", "PLACEHOLDER": "Prašome įrašyti kanalo pavadinimą", "ERROR": "<PERSON><PERSON> la<PERSON> yra privalomas"}, "LINE_CHANNEL_ID": {"LABEL": "LINE kanalo ID", "PLACEHOLDER": "LINE kanalo ID"}, "LINE_CHANNEL_SECRET": {"LABEL": "LINE Kanalo Secret", "PLACEHOLDER": "LINE Kanalo Secret"}, "LINE_CHANNEL_TOKEN": {"LABEL": "LINE Kanalo Prieigos Raktas", "PLACEHOLDER": "LINE Kanalo Prieigos Raktas"}, "SUBMIT_BUTTON": "Sukurti LINE kanalą", "API": {"ERROR_MESSAGE": "Mes negalėjome išsaugoti LINE kanalo"}, "API_CALLBACK": {"TITLE": "Automatinio atskambinimo URL", "SUBTITLE": "Turite sukonfigūruoti LINE programoje Webhook URL, su čia nurodytu URL."}}, "TELEGRAM_CHANNEL": {"TITLE": "Telegram Kanalas", "DESC": "Integruokite Telegram kanalą ir pradėkite komunikuoti su savo klientais.", "BOT_TOKEN": {"LABEL": "Boto Prieigos <PERSON>", "SUBTITLE": "Sukonfig<PERSON>ru<PERSON><PERSON> boto prieigos raktą, kurį gavote iš Telegram BotFather.", "PLACEHOLDER": "Boto Prieigos <PERSON>"}, "SUBMIT_BUTTON": "Sukurti Telegram kanalą", "API": {"ERROR_MESSAGE": "Mes negalė<PERSON> išsaugoti telegram kanalo"}}, "AUTH": {"TITLE": "<PERSON><PERSON><PERSON><PERSON>", "DESC": "AI Agent-OK palaiko <PERSON>, Facebook Messenger, Twitter profilius, WhatsApp, el. laiškus ir kt. kaip kanalus. Jei norite sukurti personalizuotą kanalą, galite jį sukurti naudodami API kanalą. <PERSON><PERSON><PERSON><PERSON>, pasirinkite vieną iš toliau pateiktų kanalų."}, "AGENTS": {"TITLE": "Agentai", "DESC": "Čia galite pridėti agentų, kad galėtumėte tvarkyti naujai sukurtus gautų laiškų aplanką. Tik šie pasirinkti agentai turės prieigą prie jūsų gautų laiškų aplanko. Agentai, kurie n<PERSON>, prisijungę negalės matyti pranešimų arba atsakyti į juos. <br> <b>PS:</b> Jei jums reikia prieigos prie visų gautų laiškų aplankų, turėtumėte įtraukti save kaip agentą prie visų sukurtų aplankų, kaip administratorius.", "VALIDATION_ERROR": "Add at least one agent to your new Inbox", "PICK_AGENTS": "Pasirinkite agentus gautų laiškų aplankams"}, "DETAILS": {"TITLE": "Gautų Laiškų Aplanko Detali Informacija", "DESC": "Žemiau esančiame meniu pasirinkite Facebook puslapį, kurį norite prisijungti prie „AI Agent-OK“. Taip pat galite priskirti norimą pavadinimą savo gautų laiškų aplankui, kad būtų lengviau atpažinti."}, "FINISH": {"TITLE": "Atlikta!", "DESC": "Sėkmingai užbaigėte savo Facebook puslapio integravimą su AI Agent-OK. Kai kitą kartą klientas parašys pranešimą jūsų puslapyje, pokalbis automatiškai pasirodys jūsų gautų laiškų aplanke.<br><PERSON>p pat pateikiame valdiklio s<PERSON>, kurį galite lengvai pridėti prie savo svetainės. Kai tai bus įdiegta jūsų svetainėje, klientai gali jums siųsti pranešimus tiesiai iš jūsų svetainės, nenaudodami jokio išorinio įrankio, o pokalbis bus rodomas AI Agent-OK.<br><PERSON><PERSON><PERSON>, ar ne? Na, mes tikrai stengiamės tokiais būti :)"}, "EMAIL_PROVIDER": {"TITLE": "Pasirinkite el. pašto paslaugų teikėją", "DESCRIPTION": "Pasirinkite el. pašto paslaugų teikėją iš toliau pateikto sąrašo. Jei sąraše nematote savo el. pašto teik<PERSON>, galite pasirinkti kitą teikėją ir pateikti IMAP/SMTP prisijungimo duomenis."}, "MICROSOFT": {"TITLE": "Microsoft El. paštas", "DESCRIPTION": "Spustelėkite mygtuką Prisijungti naudojant Microsoft, kad pradėtumėte. Būsite nukreipti į prisijungimo el. pašto pagalba puslapį. Kai sutiksite su prašomais leid<PERSON>, būsite nukreipti atgal į gautų laiškų aplanko kūrimo veiksmą.", "EMAIL_PLACEHOLDER": "Įvesti el. pa<PERSON><PERSON> ad<PERSON>", "SIGN_IN": "Sign in with Microsoft", "ERROR_MESSAGE": "Prisijungiant prie Microsoft įvyko klaida, bandykite dar kartą"}, "GOOGLE": {"TITLE": "Google Email", "DESCRIPTION": "Click on the Sign in with Google button to get started. You will redirected to the email sign in page. Once you accept the requested permissions, you would be redirected back to the inbox creation step.", "SIGN_IN": "Sign in with Google", "EMAIL_PLACEHOLDER": "Įvesti el. pa<PERSON><PERSON> ad<PERSON>", "ERROR_MESSAGE": "There was an error connecting to Google, please try again"}}, "DETAILS": {"LOADING_FB": "Autentifikuojamas jus naudojant Facebook...", "ERROR_FB_LOADING": "Error loading Facebook SDK. Please disable any ad-blockers and try again from a different browser.", "ERROR_FB_AUTH": "<PERSON><PERSON><PERSON>, atnaujinkite puslapį...", "ERROR_FB_UNAUTHORIZED": "<PERSON><PERSON><PERSON> nesate įgalioti atlikti šį veiksmą. ", "ERROR_FB_UNAUTHORIZED_HELP": "Įsitikinkite, kad turite prieigą prie „Facebook“ puslapio su visapusiška kontrole. Daugiau apie „Facebook“ vaidmenis galite per<PERSON>ityti <a href=\" https://www.facebook.com/help/187316341316631\">čia</a>.", "CREATING_CHANNEL": "Kuriamas gautų laiškų aplankas...", "TITLE": "Konfigūruoti gautų laiškų aplanko informaciją", "DESC": ""}, "AGENTS": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>us", "ADD_AGENTS": "Pridedami agentai gautų laiškų aplankams..."}, "FINISH": {"TITLE": "Jūsų Gautų Laiškų Aplankas yra paruoštas!", "MESSAGE": "Dabar galite bendrauti su klientais naudodami naująjį kanalą. Gero naudojimo", "BUTTON_TEXT": "Nuvesk mane ten", "MORE_SETTINGS": "Daugiau nustatymų", "WEBSITE_SUCCESS": "Sėkmingai baigėte kurti svetainės kanalą. Nukopijuokite toliau pateiktą kodą ir įdėkite jį į savo svetainę. Kai kitą kartą klientas naudosis tiesioginiu pokalbiu, pokalbis bus automatiškai rodomas jūsų gautų pranešimų aplanke."}, "REAUTH": "Pakartotinai autorizuoti", "VIEW": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EDIT": {"API": {"SUCCESS_MESSAGE": "Gautų laiškų aplanko nustatymai atnaujinti sėkmingai", "AUTO_ASSIGNMENT_SUCCESS_MESSAGE": "Automatinis pris<PERSON>s atna<PERSON>jin<PERSON> sėkmingai", "ERROR_MESSAGE": "Nepavyko atnaujinti gautų laiškų aplanko nustatymų. Pabandykite dar kartą vėliau."}, "EMAIL_COLLECT_BOX": {"ENABLED": "<PERSON><PERSON><PERSON>", "DISABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ENABLE_CSAT": {"ENABLED": "<PERSON><PERSON><PERSON>", "DISABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "SENDER_NAME_SECTION": {"TITLE": "<PERSON><PERSON><PERSON><PERSON>", "SUB_TEXT": "Select the name shown to your customer when they receive emails from your agents.", "FOR_EG": "Pvz:", "FRIENDLY": {"TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FROM": "nuo", "SUBTITLE": "Prie siuntėjo vardo pridėkite atsakymą išsiuntusio agento vardą, kad atsakymas būt<PERSON> draugi<PERSON>."}, "PROFESSIONAL": {"TITLE": "Profesionalas", "SUBTITLE": "Naudokite tik sukonfigūruotą įmonės pavadinimą kaip siunt<PERSON> vardą el. pašto ant<PERSON>štė<PERSON>."}, "BUSINESS_NAME": {"BUTTON_TEXT": "+ Sukonfigūruokite įmonės pavadinimą", "PLACEHOLDER": "Įveskite įmonės pavadinimą", "SAVE_BUTTON_TEXT": "<PERSON>š<PERSON>ug<PERSON><PERSON>"}}, "ALLOW_MESSAGES_AFTER_RESOLVED": {"ENABLED": "<PERSON><PERSON><PERSON>", "DISABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ENABLE_CONTINUITY_VIA_EMAIL": {"ENABLED": "<PERSON><PERSON><PERSON>", "DISABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "LOCK_TO_SINGLE_CONVERSATION": {"ENABLED": "<PERSON><PERSON><PERSON>", "DISABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ENABLE_HMAC": {"LABEL": "<PERSON><PERSON><PERSON>"}}, "DELETE": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>", "AVATAR_DELETE_BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>", "CONFIRM": {"TITLE": "<PERSON><PERSON><PERSON><PERSON>", "MESSAGE": "Ar tikrai norite <PERSON> ", "PLACE_HOLDER": "Įveskite {inboxName}, kad pat<PERSON>", "YES": "<PERSON>p, Trinti ", "NO": "Ne, Išsaugoti "}, "API": {"SUCCESS_MESSAGE": "Gautų Laiškų Aplankas ištrintas sėkmingai", "ERROR_MESSAGE": "Nepavyko ištrinti gautų laiškų aplanko. Pabandykite dar kartą vėliau.", "AVATAR_SUCCESS_MESSAGE": "Gautų Laiškų Aplanko avataras ištrintas sėk<PERSON>ai", "AVATAR_ERROR_MESSAGE": "Nepavyko ištrinti gautų laiškų aplanko avataro. Pabandykite dar kartą vėliau."}}, "TABS": {"SETTINGS": "Nustatymai", "COLLABORATORS": "Bendradarbiai", "CONFIGURATION": "Konfiguracija", "CAMPAIGN": "<PERSON><PERSON><PERSON><PERSON>", "PRE_CHAT_FORM": "<PERSON><PERSON><PERSON>, rod<PERSON> prieš pokalbio internetu pradžią", "BUSINESS_HOURS": "Darbo valandos", "WIDGET_BUILDER": "Valdiklių kūrimo priemonė", "BOT_CONFIGURATION": "<PERSON>to konfigurac<PERSON>"}, "SETTINGS": "Nustatymai", "FEATURES": {"LABEL": "Funkcijos", "DISPLAY_FILE_PICKER": "Valdiklyje rodyti failų pasirinkimą", "DISPLAY_EMOJI_PICKER": "Valdiklyje rodyti emoji pasirinkimą", "ALLOW_END_CONVERSATION": "<PERSON><PERSON><PERSON> var<PERSON> baigti pokalbį naudojant valdiklį", "USE_INBOX_AVATAR_FOR_BOT": "Botui naudokite gautų laiškų aplanko pavadinimą ir avatarą"}, "SETTINGS_POPUP": {"MESSENGER_HEADING": "<PERSON> scenarijus", "MESSENGER_SUB_HEAD": "Įdėkite šį mygtuką savo <body> <PERSON><PERSON><PERSON> viduje", "INBOX_AGENTS": "Agentai", "INBOX_AGENTS_SUB_TEXT": "Pridėti ar pašalinti agentus iš gautų laiškų aplanko", "AGENT_ASSIGNMENT": "Pokalbio <PERSON>", "AGENT_ASSIGNMENT_SUB_TEXT": "Atnaujinkite pokalbio priskirstymo nustatymus", "UPDATE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ENABLE_EMAIL_COLLECT_BOX": "Leisti el. pašto surink<PERSON>", "ENABLE_EMAIL_COLLECT_BOX_SUB_TEXT": "Naujame pokalbyje leiskite arba drauskite el. pa<PERSON>to surink<PERSON> d<PERSON>", "AUTO_ASSIGNMENT": "Įjunkti automatinį priskyrimą", "ENABLE_CSAT": "Leisti CSAT", "SENDER_NAME_SECTION": "Leisti agento vardą el. pa<PERSON>", "ENABLE_CSAT_SUB_TEXT": "Leisti/neleisti CSAT (klientų pasitenkinimo) apklausą, kai baigsite pokalbį", "SENDER_NAME_SECTION_TEXT": "Įjungti/išjungti agento vardo rod<PERSON> el. <PERSON>, j<PERSON>, bus rodomas įmonės pavadinimas", "ENABLE_CONTINUITY_VIA_EMAIL": "Leisti pokalbio tęstinumą el. paštu", "ENABLE_CONTINUITY_VIA_EMAIL_SUB_TEXT": "<PERSON><PERSON> k<PERSON> el. pašto adresas yra pasiek<PERSON>, pokalbiai bus tęsiami el. paštu.", "LOCK_TO_SINGLE_CONVERSATION": "Laikykitės vieno pokalbio", "LOCK_TO_SINGLE_CONVERSATION_SUB_TEXT": "Leisti arba neleisti kelis pokalbius tam pačiam kontaktui šiame gautų laiškų aplanke", "INBOX_UPDATE_TITLE": "Gautų Laiškų Aplanko Nustatymai", "INBOX_UPDATE_SUB_TEXT": "Atnaujinkite gautų laiškų aplanko nustatymus", "AUTO_ASSIGNMENT_SUB_TEXT": "Įjunkite arba išjunkite automatinį naujų pokalbių priskyrimą agentams, pridėtiems prie šio gautų la<PERSON>škų aplanko.", "HMAC_VERIFICATION": "<PERSON>art<PERSON><PERSON>", "HMAC_DESCRIPTION": "Naudodami šį raktą galite sugeneruoti slaptą prieigos raktą, kuris gali būti naudojamas jūsų vartotojų tapatybei patikrinti.", "HMAC_LINK_TO_DOCS": "Detaliau s<PERSON>.", "HMAC_MANDATORY_VERIFICATION": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HMAC_MANDATORY_DESCRIPTION": "If enabled, requests missing the `identifier_hash` will be rejected.", "INBOX_IDENTIFIER": "Gautų <PERSON>škų Aplanko Identifikatorius", "INBOX_IDENTIFIER_SUB_TEXT": "Norėdami patvirtinti API klientų tapatybę, naudokite čia rodomą „inbox_identifier“ prieigos raktą.", "FORWARD_EMAIL_TITLE": "Persiųsti į el. paštą", "FORWARD_EMAIL_SUB_TEXT": "Pradėkite peradresuoti savo el. laiškus toliau nurodytu el. pašto adresu.", "ALLOW_MESSAGES_AFTER_RESOLVED": "<PERSON><PERSON><PERSON> praneš<PERSON> pokal<PERSON>ui pasi<PERSON>us", "ALLOW_MESSAGES_AFTER_RESOLVED_SUB_TEXT": "Leiskite galutiniams vartotojams siųsti pranešimus net pokalbiui pasibaigus.", "WHATSAPP_SECTION_SUBHEADER": "Šis API Raktas naudojamas integracijai su WhatsApp API.", "WHATSAPP_SECTION_UPDATE_SUBHEADER": "Enter the new API key to be used for the integration with the WhatsApp APIs.", "WHATSAPP_SECTION_TITLE": "API raktas", "WHATSAPP_SECTION_UPDATE_TITLE": "Atnaujinti API raktą", "WHATSAPP_SECTION_UPDATE_PLACEHOLDER": "Įveskite naują API Raktą čia", "WHATSAPP_SECTION_UPDATE_BUTTON": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "WHATSAPP_WEBHOOK_TITLE": "Webhook Patikros <PERSON>", "WHATSAPP_WEBHOOK_SUBHEADER": "Šis prieigos raktas naudojamas „webhook“ galutinio taško autentiškumui patikrinti.", "UPDATE_PRE_CHAT_FORM_SETTINGS": "Atnaujinkite išankstinio pokalbio internetu formos nustatymus"}, "HELP_CENTER": {"LABEL": "Palaikymo centras", "PLACEHOLDER": "Pasirinkti Pagalbos Centrą", "SELECT_PLACEHOLDER": "Pasirinkti Pagalbos Centrą", "REMOVE": "Pašalinti Pagalbos Centrą", "SUB_TEXT": "Pridėkite pagalbos centrą prie gautų laiškų aplanko"}, "AUTO_ASSIGNMENT": {"MAX_ASSIGNMENT_LIMIT": "Automatinio priskyrimo limitas", "MAX_ASSIGNMENT_LIMIT_RANGE_ERROR": "Įveskite re<PERSON><PERSON><PERSON><PERSON>, didesnę nei 0", "MAX_ASSIGNMENT_LIMIT_SUB_TEXT": "Apribokite maksimalų pokalbių skaičių iš šio gautų laiškų aplanko, kuriuos galima automatiškai priskirti agentui"}, "FACEBOOK_REAUTHORIZE": {"TITLE": "Pakartotinai autorizuoti", "SUBTITLE": "Jūsų ryšys su Facebook nutrūko. Jei norite toliau nauditis paslaugo<PERSON>, iš naujo prisijunkite prie Facebook puslapio", "MESSAGE_SUCCESS": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "MESSAGE_ERROR": "Įvyko klaida, pra<PERSON><PERSON> pabandykite dar kartą"}, "PRE_CHAT_FORM": {"DESCRIPTION": "Išankstiniai poklabio ruošiniai leidžia surinkti informaciją apie vartotoją, prieš jam pradedant pokalbį su jumis.", "SET_FIELDS": "Pokalbio internetu formos laukai", "SET_FIELDS_HEADER": {"FIELDS": "Laukai", "LABEL": "Etiketė", "PLACE_HOLDER": "Rezervuota vieta", "KEY": "<PERSON><PERSON><PERSON>", "TYPE": "Tipas", "REQUIRED": "<PERSON><PERSON><PERSON><PERSON>"}, "ENABLE": {"LABEL": "Leisti poklabio internetu formą", "OPTIONS": {"ENABLED": "<PERSON><PERSON>", "DISABLED": "Ne"}}, "PRE_CHAT_MESSAGE": {"LABEL": "Pokalbio internetu pranešimas", "PLACEHOLDER": "Šis pranešimas bus matomas vartotojams kartu su forma"}, "REQUIRE_EMAIL": {"LABEL": "<PERSON>rieš pradėdami pokalbį lankytojai turėtų nurodyti savo vardą ir el. pa<PERSON>to adresą"}}, "BUSINESS_HOURS": {"TITLE": "Nustatykite savo pasiekiamumą", "SUBTITLE": "Nustatykite savo pasiekiamumą tiesioginio pokalbio valdiklyje", "WEEKLY_TITLE": "Nustatykite savaitės darbo valandas", "TIMEZONE_LABEL": "<PERSON><PERSON><PERSON><PERSON> laiko zon<PERSON>", "UPDATE": "<PERSON><PERSON><PERSON><PERSON><PERSON> darbo laiko nustatymus", "TOGGLE_AVAILABILITY": "Leisti verslo pasiek<PERSON>umą šiam gautų laiškų aplankui", "UNAVAILABLE_MESSAGE_LABEL": "Pranešimas lankytojams apie nepasiekiamumą", "TOGGLE_HELP": "<PERSON>ust<PERSON><PERSON><PERSON> darbo <PERSON>, bus rodomos darbo valandos chato valdiklyje, net jei visi agentai neprisijungę. Ne darbo valandomis lankytojai gali būti įspėti pranešimu ir išankstinio pokalbio forma.", "DAY": {"ENABLE": "Įgalinti pasiekiamumą šiai dienai", "UNAVAILABLE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HOURS": "valandos", "VALIDATION_ERROR": "<PERSON>rad<PERSON><PERSON> laikas turi būti prieš p<PERSON> laik<PERSON>.", "CHOOSE": "<PERSON><PERSON><PERSON><PERSON>"}, "ALL_DAY": "<PERSON><PERSON><PERSON>"}, "IMAP": {"TITLE": "IMAP", "SUBTITLE": "Nustatykite savo IMAP informaciją", "NOTE_TEXT": "Norėdami įjungti SMTP, sukonfigūruokite IMAP.", "UPDATE": "Atnaujinti IMAP nustatymus", "TOGGLE_AVAILABILITY": "Leisti IMAP konfigūraciją šiam gautų laiškų aplankui", "TOGGLE_HELP": "Enabling IMAP will help the user to receive email", "EDIT": {"SUCCESS_MESSAGE": "IMAP nustatymai atnaujinti sėkmingai", "ERROR_MESSAGE": "Nepavyko atnaujinti IMAP nustatymų"}, "ADDRESS": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACE_HOLDER": "Adresas (pvz.: imap.gmail.com)"}, "PORT": {"LABEL": "Portas", "PLACE_HOLDER": "Portas"}, "LOGIN": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PLACE_HOLDER": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "PASSWORD": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PLACE_HOLDER": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ENABLE_SSL": "Leisti SSL"}, "MICROSOFT": {"TITLE": "Microsoft", "SUBTITLE": "<PERSON>š naujo autorizuokite savo MICROSOFT paskyrą"}, "SMTP": {"TITLE": "SMTP", "SUBTITLE": "Nustatykite savo SMTP informaciją", "UPDATE": "Atnaujinti SMTP nustatymus", "TOGGLE_AVAILABILITY": "Leisti SMTP konfigūraciją šiam gautų laiškų aplankui", "TOGGLE_HELP": "Įjungus SMTP, vartotojas galės siųsti el. laiškus", "EDIT": {"SUCCESS_MESSAGE": "SMTP nustatymai atnaujinti sėkmingai", "ERROR_MESSAGE": "Nepavyko atnaujinti SMTP nustatymų"}, "ADDRESS": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACE_HOLDER": "Adresas (pvz.: smtp.gmail.com)"}, "PORT": {"LABEL": "Portas", "PLACE_HOLDER": "Portas"}, "LOGIN": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PLACE_HOLDER": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "PASSWORD": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PLACE_HOLDER": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "DOMAIN": {"LABEL": "<PERSON><PERSON>", "PLACE_HOLDER": "<PERSON><PERSON>"}, "ENCRYPTION": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSL_TLS": "SSL/TLS", "START_TLS": "STARTTLS", "OPEN_SSL_VERIFY_MODE": "Open SSL Patikros <PERSON>", "AUTH_MECHANISM": "Autentifikacija"}, "NOTE": "Pastaba: ", "WIDGET_BUILDER": {"WIDGET_OPTIONS": {"AVATAR": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DELETE": {"API": {"SUCCESS_MESSAGE": "<PERSON><PERSON><PERSON>", "ERROR_MESSAGE": "Įvyko klaida, pra<PERSON><PERSON> pabandykite dar kartą"}}}, "WEBSITE_NAME": {"LABEL": "Intenetinio <PERSON> pavadin<PERSON>s", "PLACE_HOLDER": "Įveskite savo internetinio puslapio pavadinimą (pvz.: Acme Inc)", "ERROR": "Prašome įrašyti teisingą internetinės svetainės pavadinimą"}, "WELCOME_HEADING": {"LABEL": "Pasisveikinimo <PERSON>š<PERSON>ė", "PLACE_HOLDER": "Sveiki!"}, "WELCOME_TAGLINE": {"LABEL": "Pasisveikinimo tekstas", "PLACE_HOLDER": "Su mumis susisiekti yra paprasta. Klauskite mūsų bet ko arba pasidalykite savo atsiliepimais."}, "REPLY_TIME": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "IN_A_FEW_MINUTES": "Per kelias minutes", "IN_A_FEW_HOURS": "Per kelias minutes", "IN_A_DAY": "Per dien<PERSON>"}, "WIDGET_COLOR_LABEL": "<PERSON><PERSON><PERSON><PERSON>", "WIDGET_BUBBLE_POSITION_LABEL": "Widget B<PERSON><PERSON>", "WIDGET_BUBBLE_TYPE_LABEL": "Widget Bubble Tipas", "WIDGET_BUBBLE_LAUNCHER_TITLE": {"DEFAULT": "<PERSON><PERSON><PERSON><PERSON><PERSON> su mumis", "LABEL": "Widget Bubble Paleidimo programos pavadinimas", "PLACE_HOLDER": "<PERSON><PERSON><PERSON><PERSON><PERSON> su mumis"}, "UPDATE": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON><PERSON> vald<PERSON><PERSON> nustat<PERSON>us", "API": {"SUCCESS_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> atnaujinti <PERSON>k<PERSON>", "ERROR_MESSAGE": "Nepavyko <PERSON>ti valdiklio nustatymų"}}, "WIDGET_VIEW_OPTION": {"PREVIEW": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SCRIPT": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "WIDGET_BUBBLE_POSITION": {"LEFT": "Kair<PERSON>", "RIGHT": "<PERSON><PERSON><PERSON><PERSON>"}, "WIDGET_BUBBLE_TYPE": {"STANDARD": "Standartas", "EXPANDED_BUBBLE": "<PERSON>š<PERSON><PERSON><PERSON><PERSON><PERSON> bur<PERSON>s"}}, "WIDGET_SCREEN": {"DEFAULT": "Pagal nutyl<PERSON>", "CHAT": "Pokalbis internetu"}, "REPLY_TIME": {"IN_A_FEW_MINUTES": "Papra<PERSON>i atsako per kelias minutes", "IN_A_FEW_HOURS": "Paprastai atsako per kelias valandas", "IN_A_DAY": "Paprastai atsako per dieną"}, "FOOTER": {"START_CONVERSATION_BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON> Pokalbį", "CHAT_INPUT_PLACEHOLDER": "Parašykite pranešimą"}, "BODY": {"TEAM_AVAILABILITY": {"ONLINE": "<PERSON><PERSON>", "OFFLINE": "Šiuo metu esame atsijungę"}, "USER_MESSAGE": "Hi", "AGENT_MESSAGE": "Sveiki"}, "BRANDING_TEXT": "Parengta pagal AI Agent-OK", "SCRIPT_SETTINGS": "\n      window.AI Agent-OKSettings = {options};"}, "EMAIL_PROVIDERS": {"MICROSOFT": "Microsoft", "GOOGLE": "Google", "OTHER_PROVIDERS": "<PERSON><PERSON>"}, "CHANNELS": {"MESSENGER": "<PERSON>", "WEB_WIDGET": "<PERSON><PERSON><PERSON> pu<PERSON>", "TWITTER_PROFILE": "Twitter", "TWILIO_SMS": "<PERSON><PERSON><PERSON>", "WHATSAPP": "WhatsApp", "SMS": "SMS", "EMAIL": "El. <PERSON>", "TELEGRAM": "Telegram", "LINE": "Line", "API": "API kanalas"}}}