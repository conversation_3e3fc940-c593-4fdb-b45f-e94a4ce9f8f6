{"AUDIT_LOGS": {"HEADER": "Journaux d'audit", "HEADER_BTN_TXT": "Ajouter des journaux d'audit", "LOADING": "Récupération des journaux d'audit", "DESCRIPTION": "Audit Logs maintain a record of activities in your account, allowing you to track and audit your account, team, or services.", "LEARN_MORE": "Learn more about audit logs", "SEARCH_404": "Il n'y a aucun élément correspondant à cette requête", "SIDEBAR_TXT": "</p><b>Les journaux d'audit</b> </p><p>Les journaux d'audit contiennent des événements et des actions associés un système AI Agent-OK. </p>", "LIST": {"404": "Il n'y a aucun journal d'audit disponible dans ce compte.", "TITLE": "<PERSON><PERSON>rer les journaux d’audit", "DESC": "Les journaux d'audit sont des traces pour les événements et les actions dans un système AI Agent-OK.", "TABLE_HEADER": {"ACTIVITY": "User", "TIME": "Action", "IP_ADDRESS": "Adresse IP"}}, "API": {"SUCCESS_MESSAGE": "Les journaux d'audit ont bien été récupérés", "ERROR_MESSAGE": "Impossible de se connecter au serveur Woot, ve<PERSON><PERSON><PERSON> réessayer plus tard"}, "DEFAULT_USER": "Système", "AUTOMATION_RULE": {"ADD": "{agentName} created a new automation rule (#{id})", "EDIT": "{agentName} updated an automation rule (#{id})", "DELETE": "{agent<PERSON>ame} deleted an automation rule (#{id})"}, "ACCOUNT_USER": {"ADD": "{agent<PERSON><PERSON>} a invité {invitee} à rejoindre le compte en tant que {role}", "EDIT": {"SELF": "{agentName} a changé sa {attributes} en {values}", "OTHER": "{agentName} a changé {attributes} de {user} en {values}", "DELETED": "{agent<PERSON><PERSON>} changed {attributes} of a deleted user to {values}"}}, "INBOX": {"ADD": "{agentName} created a new inbox (#{id})", "EDIT": "{agentName} updated an inbox (#{id})", "DELETE": "{agent<PERSON>ame} deleted an inbox (#{id})"}, "WEBHOOK": {"ADD": "{agent<PERSON>ame} created a new webhook (#{id})", "EDIT": "{agent<PERSON>ame} updated a webhook (#{id})", "DELETE": "{agent<PERSON><PERSON>} deleted a webhook (#{id})"}, "USER_ACTION": {"SIGN_IN": "{agent<PERSON>ame} s'est connecté", "SIGN_OUT": "{agentName} s'est déconnecté"}, "TEAM": {"ADD": "{<PERSON><PERSON><PERSON>} created a new team (#{id})", "EDIT": "{<PERSON><PERSON><PERSON>} updated a team (#{id})", "DELETE": "{<PERSON><PERSON><PERSON>} deleted a team (#{id})"}, "MACRO": {"ADD": "{agent<PERSON>ame} created a new macro (#{id})", "EDIT": "{agent<PERSON>ame} updated a macro (#{id})", "DELETE": "{agent<PERSON>ame} deleted a macro (#{id})"}, "INBOX_MEMBER": {"ADD": "{agent<PERSON>ame} added {user} to the inbox(#{inbox_id})", "REMOVE": "{<PERSON><PERSON><PERSON>} removed {user} from the inbox(#{inbox_id})"}, "TEAM_MEMBER": {"ADD": "{<PERSON><PERSON><PERSON>} added {user} to the team(#{team_id})", "REMOVE": "{<PERSON><PERSON><PERSON>} removed {user} from the team(#{team_id})"}, "ACCOUNT": {"EDIT": "{<PERSON><PERSON><PERSON>} updated the account configuration (#{id})"}}}