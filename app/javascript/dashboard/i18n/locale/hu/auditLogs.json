{"AUDIT_LOGS": {"HEADER": "Audit logok", "HEADER_BTN_TXT": "Audit logok hozzáadása", "LOADING": "Audit logok betöltése", "DESCRIPTION": "Audit Logs maintain a record of activities in your account, allowing you to track and audit your account, team, or services.", "LEARN_MORE": "Learn more about audit logs", "SEARCH_404": "<PERSON><PERSON><PERSON> meg<PERSON> elem", "SIDEBAR_TXT": "<p><b>Auditnaplók</b> </p><p> <PERSON><PERSON> auditnaplók a AI Agent-OK rendszer eseményeinek és műveleteinek nyomvonalai. </p>", "LIST": {"404": "<PERSON><PERSON><PERSON> Auditnapló ebben a fiókban.", "TITLE": "Audit logok menedzselése", "DESC": "Az Auditnaplók a AI Agent-OK rendszer eseményeinek és műveleteinek nyomvonalai.", "TABLE_HEADER": {"ACTIVITY": "User", "TIME": "Action", "IP_ADDRESS": "IP cím"}}, "API": {"SUCCESS_MESSAGE": "Auditnapló <PERSON>", "ERROR_MESSAGE": "<PERSON><PERSON> csatlakozni a Woot szerverhez, kérjük pr<PERSON><PERSON><PERSON><PERSON> k<PERSON>"}, "DEFAULT_USER": "Rendszer", "AUTOMATION_RULE": {"ADD": "{agentName} created a new automation rule (#{id})", "EDIT": "{agentName} updated an automation rule (#{id})", "DELETE": "{agent<PERSON>ame} deleted an automation rule (#{id})"}, "ACCOUNT_USER": {"ADD": "{agent<PERSON><PERSON>} meghívta {invitee} a fiók<PERSON>, mint {role}", "EDIT": {"SELF": "{agentName} megváltoztatta az {attributes} -aikat {values} - ra", "OTHER": "{agentName} megváltoztatta {attributes}  {user}  {values} -ra", "DELETED": "{agent<PERSON><PERSON>} changed {attributes} of a deleted user to {values}"}}, "INBOX": {"ADD": "{agentName} created a new inbox (#{id})", "EDIT": "{agentName} updated an inbox (#{id})", "DELETE": "{agent<PERSON>ame} deleted an inbox (#{id})"}, "WEBHOOK": {"ADD": "{agent<PERSON>ame} created a new webhook (#{id})", "EDIT": "{agent<PERSON>ame} updated a webhook (#{id})", "DELETE": "{agent<PERSON><PERSON>} deleted a webhook (#{id})"}, "USER_ACTION": {"SIGN_IN": "{agentName} aláírta", "SIGN_OUT": "{agent<PERSON>ame} kije<PERSON><PERSON><PERSON>tt"}, "TEAM": {"ADD": "{<PERSON><PERSON><PERSON>} created a new team (#{id})", "EDIT": "{<PERSON><PERSON><PERSON>} updated a team (#{id})", "DELETE": "{<PERSON><PERSON><PERSON>} deleted a team (#{id})"}, "MACRO": {"ADD": "{agent<PERSON>ame} created a new macro (#{id})", "EDIT": "{agent<PERSON>ame} updated a macro (#{id})", "DELETE": "{agent<PERSON>ame} deleted a macro (#{id})"}, "INBOX_MEMBER": {"ADD": "{agent<PERSON>ame} added {user} to the inbox(#{inbox_id})", "REMOVE": "{<PERSON><PERSON><PERSON>} removed {user} from the inbox(#{inbox_id})"}, "TEAM_MEMBER": {"ADD": "{<PERSON><PERSON><PERSON>} added {user} to the team(#{team_id})", "REMOVE": "{<PERSON><PERSON><PERSON>} removed {user} from the team(#{team_id})"}, "ACCOUNT": {"EDIT": "{<PERSON><PERSON><PERSON>} updated the account configuration (#{id})"}}}