{"INTEGRATION_SETTINGS": {"SHOPIFY": {"DELETE": {"TITLE": "Delete Shopify Integration", "MESSAGE": "Are you sure you want to delete the Shopify integration?"}, "STORE_URL": {"TITLE": "Connect Shopify Store", "LABEL": "Store URL", "PLACEHOLDER": "your-store.myshopify.com", "HELP": "Enter your Shopify store's myshopify.com URL", "CANCEL": "annulla", "SUBMIT": "Connect Store"}, "ERROR": "There was an error connecting to Shopify. Please try again or contact support if the issue persists."}, "HEADER": "Integrazioni", "DESCRIPTION": "AI Agent-OK integrates with multiple tools and services to improve your team's efficiency. Explore the list below to configure your favorite apps.", "LEARN_MORE": "Learn more about integrations", "LOADING": "Fetching integrations", "CAPTAIN": {"DISABLED": "Captain is not enabled on your account.", "CLICK_HERE_TO_CONFIGURE": "Click here to configure", "LOADING_CONSOLE": "Loading Captain <PERSON><PERSON><PERSON>...", "FAILED_TO_LOAD_CONSOLE": "Failed to load Captain <PERSON><PERSON>. Please refresh and try again."}, "WEBHOOK": {"SUBSCRIBED_EVENTS": "<PERSON><PERSON> is<PERSON>ti", "LEARN_MORE": "Learn more about webhooks", "FORM": {"CANCEL": "<PERSON><PERSON><PERSON>", "DESC": "Gli eventi Webhook ti forniscono le informazioni in tempo reale su ciò che sta accadendo nel tuo account AI Agent-OK. Per favore inserisci un URL valido per configurare un callback.", "SUBSCRIPTIONS": {"LABEL": "Eventi", "EVENTS": {"CONVERSATION_CREATED": "Conversazione creata", "CONVERSATION_STATUS_CHANGED": "Stato conversazione cambiato", "CONVERSATION_UPDATED": "Conversazione aggiornata", "MESSAGE_CREATED": "Messaggio creato", "MESSAGE_UPDATED": "Messaggio aggiornato", "WEBWIDGET_TRIGGERED": "Widget live chat aperto dall'utente", "CONTACT_CREATED": "Contact created", "CONTACT_UPDATED": "Contact updated"}}, "END_POINT": {"LABEL": "URL del webhook", "PLACEHOLDER": "Example: {webhookExampleURL}", "ERROR": "Inserisci un URL valido"}, "EDIT_SUBMIT": "Aggiorna webhook", "ADD_SUBMIT": "<PERSON><PERSON> webhook"}, "TITLE": "Webhook", "CONFIGURE": "Configura", "HEADER": "Impostazioni Webhook", "HEADER_BTN_TXT": "Aggiungi nuovo webhook", "LOADING": "Recupero dei webhooks collegati", "SEARCH_404": "Non ci sono elementi che corrispondono a questa richiesta", "SIDEBAR_TXT": "<p><b>Webhooks</b> </p> <p>I Webhooks sono callback HTTP che possono essere definiti per ogni account. Sono innescati da eventi come la creazione di messaggi in AI Agent-OK. Puoi creare più di un webhook per questo account. <br /><br /> Per creare un <b>webhook</b>, clicca sul pulsante <b>Aggiungi nuovo webhook</b>. Puoi anche rimuovere qualsiasi webhook esistente cliccando sul pulsante Elimina.</p>", "LIST": {"404": "Non ci sono webhook configurati per questo account.", "TITLE": "Gestisci webhook", "TABLE_HEADER": {"WEBHOOK_ENDPOINT": "Endpoint Webhook", "ACTIONS": "Azioni"}}, "EDIT": {"BUTTON_TEXT": "Modifica", "TITLE": "Modifica webhook", "API": {"SUCCESS_MESSAGE": "Configurazione Webhook aggiornata correttamente", "ERROR_MESSAGE": "Impossibile connettersi al server Woot, riprova più tardi"}}, "ADD": {"CANCEL": "<PERSON><PERSON><PERSON>", "TITLE": "Aggiungi nuovo webhook", "API": {"SUCCESS_MESSAGE": "Configurazione Webhook aggiunta correttamente", "ERROR_MESSAGE": "Impossibile connettersi al server Woot, riprova più tardi"}}, "DELETE": {"BUTTON_TEXT": "Elimina", "API": {"SUCCESS_MESSAGE": "Webhook eliminato con successo", "ERROR_MESSAGE": "Impossibile connettersi al server Woot, riprova più tardi"}, "CONFIRM": {"TITLE": "Conferma eliminazione", "MESSAGE": "Sei sicuro di voler eliminare il webhook? ({webhookURL})", "YES": "Sì, elimina ", "NO": "No, conserva"}}}, "SLACK": {"DELETE": "Elimina", "DELETE_CONFIRMATION": {"TITLE": "Delete the integration", "MESSAGE": "Are you sure you want to delete the integration? Doing so will result in the loss of access to conversations on your Slack workspace."}, "HELP_TEXT": {"TITLE": "Stai utilizzando l'integrazione Slack", "BODY": "With this integration, all of your incoming conversations will be synced to the ***{selectedChannel<PERSON><PERSON>}*** channel in your Slack workspace. You can manage all your customer conversations right within the channel and never miss a message.\n\nHere are the main features of the integration:\n\n**Respond to conversations from within Slack:** To respond to a conversation in the ***{selectedChannelName}*** Slack channel, simply type out your message and send it as a thread. This will create a response back to the customer through AI Agent-OK. It's that simple!\n\n **Create private notes:** If you want to create private notes instead of replies, start your message with ***`note:`***. This ensures that your message is kept private and won't be visible to the customer.\n\n**Associate an agent profile:** If the person who replied on Slack has an agent profile in AI Agent-OK under the same email, the replies will be associated with that agent profile automatically. This means you can easily track who said what and when. On the other hand, when the replier doesn't have an associated agent profile, the replies will appear from the bot profile to the customer.", "SELECTED": "selected"}, "SELECT_CHANNEL": {"OPTION_LABEL": "Select a channel", "UPDATE": "Aggiorna", "BUTTON_TEXT": "Connect channel", "DESCRIPTION": "Your Slack workspace is now linked with AI Agent-OK. However, the integration is currently inactive. To activate the integration and connect a channel to AI Agent-OK, please click the button below.\n\n**Note:** If you are attempting to connect a private channel, add the AI Agent-OK app to the Slack channel before proceeding with this step.", "ATTENTION_REQUIRED": "Attention required", "EXPIRED": "Your Slack integration has expired. To continue receiving messages on Slack, please delete the integration and connect your workspace again."}, "UPDATE_ERROR": "There was an error updating the integration, please try again", "UPDATE_SUCCESS": "The channel is connected successfully", "FAILED_TO_FETCH_CHANNELS": "There was an error fetching the channels from Slack, please try again"}, "DYTE": {"CLICK_HERE_TO_JOIN": "Click here to join", "LEAVE_THE_ROOM": "Leave the room", "START_VIDEO_CALL_HELP_TEXT": "Start a new video call with the customer", "JOIN_ERROR": "There was an error joining the call, please try again", "CREATE_ERROR": "There was an error creating a meeting link, please try again"}, "OPEN_AI": {"AI_ASSIST": "AI Assist", "WITH_AI": " {option} with AI ", "OPTIONS": {"REPLY_SUGGESTION": "Reply Suggestion", "SUMMARIZE": "Summarize", "REPHRASE": "Improve Writing", "FIX_SPELLING_GRAMMAR": "Fix Spelling and Grammar", "SHORTEN": "<PERSON>en", "EXPAND": "Expand", "MAKE_FRIENDLY": "Change message tone to friendly", "MAKE_FORMAL": "Use formal tone", "SIMPLIFY": "Simplify"}, "ASSISTANCE_MODAL": {"DRAFT_TITLE": "Draft content", "GENERATED_TITLE": "Generated content", "AI_WRITING": "AI is writing", "BUTTONS": {"APPLY": "Use this suggestion", "CANCEL": "annulla"}}, "CTA_MODAL": {"TITLE": "Integrate with OpenAI", "DESC": "Bring advanced AI features to your dashboard with OpenAI's GPT models. To begin, enter the API key from your OpenAI account.", "KEY_PLACEHOLDER": "Enter your OpenAI API key", "BUTTONS": {"NEED_HELP": "Hai bisogno di aiuto?", "DISMISS": "<PERSON><PERSON><PERSON>", "FINISH": "Finish Setup"}, "DISMISS_MESSAGE": "You can setup OpenAI integration later Whenever you want.", "SUCCESS_MESSAGE": "OpenAI integration setup successfully"}, "TITLE": "Improve With AI", "SUMMARY_TITLE": "Summary with AI", "REPLY_TITLE": "Reply suggestion with AI", "SUBTITLE": "An improved reply will be generated using AI, based on your current draft.", "TONE": {"TITLE": "<PERSON><PERSON>", "OPTIONS": {"PROFESSIONAL": "Professional", "FRIENDLY": "Friendly"}}, "BUTTONS": {"GENERATE": "Generate", "GENERATING": "Generating...", "CANCEL": "<PERSON><PERSON><PERSON>"}, "GENERATE_ERROR": "There was an error processing the content, please try again"}, "DELETE": {"BUTTON_TEXT": "Elimina", "API": {"SUCCESS_MESSAGE": "Integrazione eliminata con successo"}}, "CONNECT": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON>"}, "DASHBOARD_APPS": {"TITLE": "App dashboard", "HEADER_BTN_TXT": "Aggiungi una nuova app dashboard", "SIDEBAR_TXT": "<p><b>App dashboard</b></p><p>Le app dashboard consentono alle organizzazioni di incorporare un'applicazione all'interno del cruscotto AI Agent-OK per fornire il contesto per gli agenti di assistenza clienti. Questa funzione consente di creare un'applicazione in modo indipendente e incorporata all'interno della dashboard per fornire informazioni all'utente, i loro ordini, o la loro cronologia di pagamento precedente.</p><p>Quando hai incorporato la tua applicazione usando il cruscotto in AI Agent-OK, la tua applicazione avrà il contesto della conversazione e del contatto come evento finestra. Implementa un ascoltatore per l'evento del messaggio sulla tua pagina per ricevere il contesto.</p><p>Per aggiungere una nuova app dashboard, clicca sul pulsante 'Aggiungi una nuova app dashboard'.</p>", "DESCRIPTION": "Le app dashboard consentono alle organizzazioni di incorporare un'applicazione all'interno della dashboard per fornire il contesto per gli agenti di assistenza clienti. Questa funzione consente di creare un'applicazione in modo indipendente e incorporare che per fornire informazioni sull'utente, i suoi ordini o la loro cronologia di pagamento precedente.", "LEARN_MORE": "Learn more about Dashboard Apps", "LIST": {"404": "Non ci sono ancora app dashboard configurate su questo account", "LOADING": "Recupero delle app dashboard...", "TABLE_HEADER": {"NAME": "Nome", "ENDPOINT": "Endpoint"}, "EDIT_TOOLTIP": "Modifica app", "DELETE_TOOLTIP": "Elimina app"}, "FORM": {"TITLE_LABEL": "Nome", "TITLE_PLACEHOLDER": "Inserisci un nome per la tua app dashboard", "TITLE_ERROR": "È richiesto un nome per l'app dashboard", "URL_LABEL": "Endpoint", "URL_PLACEHOLDER": "Inserisci l'URL dell'endpoint dove la tua app è ospitata", "URL_ERROR": "È richiesto un URL valido"}, "CREATE": {"HEADER": "Aggiungi una nuova app dashboard", "FORM_SUBMIT": "Invia", "FORM_CANCEL": "<PERSON><PERSON><PERSON>", "API_SUCCESS": "App dashboard configurata con successo", "API_ERROR": "Non siamo riusciti a creare un'app. Riprova più tardi"}, "UPDATE": {"HEADER": "Modifica app dashboard", "FORM_SUBMIT": "Aggiorna", "FORM_CANCEL": "<PERSON><PERSON><PERSON>", "API_SUCCESS": "App dashboard aggiornata con successo", "API_ERROR": "Non è stato possibile aggiornare l'app. Riprova più tardi"}, "DELETE": {"CONFIRM_YES": "Sì, eliminalo", "CONFIRM_NO": "No, mantienilo", "TITLE": "Conferma eliminazione", "MESSAGE": "Sei sicuro di voler eliminare l'app - {appName}?", "API_SUCCESS": "App dashboard cancellata con successo", "API_ERROR": "Non è stato possibile eliminare l'app. Riprova più tardi"}}, "LINEAR": {"ADD_OR_LINK_BUTTON": "Create/Link Linear Issue", "LOADING": "Fetching linear issues...", "LOADING_ERROR": "There was an error fetching the linear issues, please try again", "CREATE": "<PERSON><PERSON>", "LINK": {"SEARCH": "Search issues", "SELECT": "Select issue", "TITLE": "Link", "EMPTY_LIST": "No linear issues found", "LOADING": "Loading", "ERROR": "There was an error fetching the linear issues, please try again", "LINK_SUCCESS": "Issue linked successfully", "LINK_ERROR": "There was an error linking the issue, please try again", "LINK_TITLE": "Conversation (#{conversationId}) with {name}"}, "ADD_OR_LINK": {"TITLE": "Create/link linear issue", "DESCRIPTION": "Create Linear issues from conversations, or link existing ones for seamless tracking.", "FORM": {"TITLE": {"LABEL": "<PERSON><PERSON>", "PLACEHOLDER": "Enter title", "REQUIRED_ERROR": "Il titolo è obbligatorio"}, "DESCRIPTION": {"LABEL": "Descrizione", "PLACEHOLDER": "Enter description"}, "TEAM": {"LABEL": "Team", "PLACEHOLDER": "Seleziona team", "SEARCH": "Search team", "REQUIRED_ERROR": "Team is required"}, "ASSIGNEE": {"LABEL": "Assignee", "PLACEHOLDER": "Select assignee", "SEARCH": "Search assignee"}, "PRIORITY": {"LABEL": "Priorità", "PLACEHOLDER": "Select priority", "SEARCH": "Search priority"}, "LABEL": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Select label", "SEARCH": "Search label"}, "STATUS": {"LABEL": "Stato", "PLACEHOLDER": "Select status", "SEARCH": "Search status"}, "PROJECT": {"LABEL": "Project", "PLACEHOLDER": "Select project", "SEARCH": "Search project"}}, "CREATE": "<PERSON><PERSON>", "CANCEL": "annulla", "CREATE_SUCCESS": "Issue created successfully", "CREATE_ERROR": "There was an error creating the issue, please try again", "LOADING_TEAM_ERROR": "There was an error fetching the teams, please try again", "LOADING_TEAM_ENTITIES_ERROR": "There was an error fetching the team entities, please try again"}, "ISSUE": {"STATUS": "Stato", "PRIORITY": "Priorità", "ASSIGNEE": "Assignee", "LABELS": "<PERSON><PERSON><PERSON><PERSON>", "CREATED_AT": "Created at {createdAt}"}, "UNLINK": {"TITLE": "Unlink", "SUCCESS": "Issue unlinked successfully", "ERROR": "There was an error unlinking the issue, please try again"}, "DELETE": {"TITLE": "Are you sure you want to delete the integration?", "MESSAGE": "Are you sure you want to delete the integration?", "CONFIRM": "Yes, delete", "CANCEL": "annulla"}}}, "CAPTAIN": {"NAME": "Captain", "HEADER_KNOW_MORE": "Know more", "COPILOT": {"SEND_MESSAGE": "Invia messaggio...", "LOADER": "Captain is thinking", "YOU": "You", "USE": "Use this", "RESET": "Reset", "SELECT_ASSISTANT": "Select Assistant"}, "PAYWALL": {"TITLE": "Upgrade to use Captain AI", "AVAILABLE_ON": "Captain is not available on the free plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "UPGRADE_NOW": "Upgrade now", "CANCEL_ANYTIME": "You can change or cancel your plan anytime"}, "ENTERPRISE_PAYWALL": {"AVAILABLE_ON": "Captain AI feature is only available in a paid plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "ASK_ADMIN": "Please reach out to your administrator for the upgrade."}, "BANNER": {"RESPONSES": "You've used over 80% of your response limit. To continue using Captain AI, please upgrade.", "DOCUMENTS": "Document limit reached. Upgrade to continue using Captain AI."}, "FORM": {"CANCEL": "annulla", "CREATE": "<PERSON><PERSON>", "EDIT": "Aggiorna"}, "ASSISTANTS": {"HEADER": "Assistants", "ADD_NEW": "Create a new assistant", "DELETE": {"TITLE": "Are you sure to delete the assistant?", "DESCRIPTION": "This action is permanent. Deleting this assistant will remove it from all connected inboxes and permanently erase all generated knowledge.", "CONFIRM": "Yes, delete", "SUCCESS_MESSAGE": "The assistant has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the assistant, please try again."}, "FORM_DESCRIPTION": "Fill out the details below to name your assistant, describe its purpose, and specify the product it will support.", "CREATE": {"TITLE": "Create an assistant", "SUCCESS_MESSAGE": "The assistant has been successfully created", "ERROR_MESSAGE": "There was an error creating the assistant, please try again."}, "FORM": {"NAME": {"LABEL": "Assistant Name", "PLACEHOLDER": "Enter a name for the assistant", "ERROR": "Please provide a name for the assistant"}, "DESCRIPTION": {"LABEL": "Assistant Description", "PLACEHOLDER": "Describe how and where this assistant will be used", "ERROR": "A description is required"}, "PRODUCT_NAME": {"LABEL": "Product Name", "PLACEHOLDER": "Enter the name of the product this assistant is designed for", "ERROR": "The product name is required"}, "FEATURES": {"TITLE": "Funzionalità", "ALLOW_CONVERSATION_FAQS": "Generate FAQs from resolved conversations", "ALLOW_MEMORIES": "Capture key details as memories from customer interactions."}}, "EDIT": {"TITLE": "Update the assistant", "SUCCESS_MESSAGE": "The assistant has been successfully updated", "ERROR_MESSAGE": "There was an error updating the assistant, please try again."}, "OPTIONS": {"EDIT_ASSISTANT": "Edit Assistant", "DELETE_ASSISTANT": "Delete Assistant", "VIEW_CONNECTED_INBOXES": "View connected inboxes"}, "EMPTY_STATE": {"TITLE": "No assistants available", "SUBTITLE": "Create an assistant to provide quick and accurate responses to your users. It can learn from your help articles and past conversations.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain Assistant", "NOTE": "Captain Assistant engages directly with customers, learns from your help docs and past conversations, and delivers instant, accurate responses. It handles the initial queries, providing quick resolutions before  transferring to an agent when needed."}}}, "DOCUMENTS": {"HEADER": "Documents", "ADD_NEW": "Create a new document", "RELATED_RESPONSES": {"TITLE": "Related FAQs", "DESCRIPTION": "These FAQs are generated directly from the document."}, "FORM_DESCRIPTION": "Enter the URL of the document to add it as a knowledge source and choose the assistant to associate it with.", "CREATE": {"TITLE": "Add a document", "SUCCESS_MESSAGE": "The document has been successfully created", "ERROR_MESSAGE": "There was an error creating the document, please try again."}, "FORM": {"URL": {"LABEL": "URL", "PLACEHOLDER": "Enter the URL of the document", "ERROR": "Please provide a valid URL for the document"}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select the assistant", "ERROR": "The assistant field is required"}}, "DELETE": {"TITLE": "Are you sure to delete the document?", "DESCRIPTION": "This action is permanent. Deleting this document will permanently erase all generated knowledge.", "CONFIRM": "Yes, delete", "SUCCESS_MESSAGE": "The document has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the document, please try again."}, "OPTIONS": {"VIEW_RELATED_RESPONSES": "View Related Responses", "DELETE_DOCUMENT": "Delete Document"}, "EMPTY_STATE": {"TITLE": "No documents available", "SUBTITLE": "Documents are used by your assistant to generate FAQs. You can import documents to provide context for your assistant.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain <PERSON>ument", "NOTE": "A document in Captain serves as a knowledge resource for the assistant. By connecting your help center or guides, Captain can analyze the content and provide accurate responses for customer inquiries."}}}, "RESPONSES": {"HEADER": "FAQs", "ADD_NEW": "Create new FAQ", "DOCUMENTABLE": {"CONVERSATION": "Conversation #{id}"}, "SELECTED": "{count} selected", "BULK_APPROVE_BUTTON": "Approve", "BULK_DELETE_BUTTON": "Elimina", "BULK_APPROVE": {"SUCCESS_MESSAGE": "FAQs approved successfully", "ERROR_MESSAGE": "There was an error approving the FAQs, please try again."}, "BULK_DELETE": {"TITLE": "Delete FAQs?", "DESCRIPTION": "Are you sure you want to delete the selected FAQs? This action cannot be undone.", "CONFIRM": "Yes, delete all", "SUCCESS_MESSAGE": "FAQs deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQs, please try again."}, "DELETE": {"TITLE": "Are you sure to delete the FAQ?", "DESCRIPTION": "", "CONFIRM": "Yes, delete", "SUCCESS_MESSAGE": "FAQ deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQ, please try again."}, "FILTER": {"ASSISTANT": "Assistant: {selected}", "STATUS": "Status: {selected}", "ALL_ASSISTANTS": "<PERSON><PERSON>"}, "STATUS": {"TITLE": "Stato", "PENDING": "In sospeso", "APPROVED": "Approved", "ALL": "<PERSON><PERSON>"}, "FORM_DESCRIPTION": "Add a question and its corresponding answer to the knowledge base and select the assistant it should be associated with.", "CREATE": {"TITLE": "Add an FAQ", "SUCCESS_MESSAGE": "The response has been added successfully.", "ERROR_MESSAGE": "An error occurred while adding the response. Please try again."}, "FORM": {"QUESTION": {"LABEL": "Question", "PLACEHOLDER": "Enter the question here", "ERROR": "Please provide a valid question."}, "ANSWER": {"LABEL": "Answer", "PLACEHOLDER": "Enter the answer here", "ERROR": "Please provide a valid answer."}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select an assistant", "ERROR": "Please select an assistant."}}, "EDIT": {"TITLE": "Update the FAQ", "SUCCESS_MESSAGE": "The FAQ has been successfully updated", "ERROR_MESSAGE": "There was an error updating the FAQ, please try again", "APPROVE_SUCCESS_MESSAGE": "The FAQ was marked as approved"}, "OPTIONS": {"APPROVE": "Mark as approved", "EDIT_RESPONSE": "Edit FAQ", "DELETE_RESPONSE": "Delete FAQ"}, "EMPTY_STATE": {"TITLE": "No FAQs Found", "SUBTITLE": "FAQs help your assistant provide quick and accurate answers to questions from your customers. They can be generated automatically from your content or can be added manually.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain FAQ", "NOTE": "Captain <PERSON><PERSON><PERSON> detects common customer questions—whether missing from your knowledge base or frequently asked—and generates relevant FAQs to improve support. You can review each suggestion and decide whether to approve or reject it."}}}, "INBOXES": {"HEADER": "Connected Inboxes", "ADD_NEW": "Connect a new inbox", "OPTIONS": {"DISCONNECT": "<PERSON><PERSON><PERSON>"}, "DELETE": {"TITLE": "Are you sure to disconnect the inbox?", "DESCRIPTION": "", "CONFIRM": "Yes, delete", "SUCCESS_MESSAGE": "The inbox was successfully disconnected.", "ERROR_MESSAGE": "There was an error disconnecting the inbox, please try again."}, "FORM_DESCRIPTION": "Choose an inbox to connect with the assistant.", "CREATE": {"TITLE": "Connect an Inbox", "SUCCESS_MESSAGE": "The inbox was successfully connected.", "ERROR_MESSAGE": "An error occurred while connecting the inbox. Please try again."}, "FORM": {"INBOX": {"LABEL": "<PERSON><PERSON>", "PLACEHOLDER": "Choose the inbox to deploy the assistant.", "ERROR": "An inbox selection is required."}}, "EMPTY_STATE": {"TITLE": "No Connected Inboxes", "SUBTITLE": "Connecting an inbox allows the assistant to handle initial questions from your customers before transferring them to you."}}}}