{"INBOX_MGMT": {"HEADER": "Kotak masuk", "DESCRIPTION": "A channel is the mode of communication your customer chooses to interact with you. An inbox is where you manage interactions for a specific channel. It can include communications from various sources such as email, live chat, and social media.", "LEARN_MORE": "Learn more about inboxes", "RECONNECTION_REQUIRED": "Your inbox is disconnected. You won't receive new messages until you reauthorize it.", "CLICK_TO_RECONNECT": "Click here to reconnect.", "LIST": {"404": "Tidak ada kotak masuk yang dilampirkan ke akun ini."}, "CREATE_FLOW": {"CHANNEL": {"TITLE": "Pilih Channel", "BODY": "<PERSON><PERSON><PERSON> penyedia yang ingin Anda integrasikan dengan AI Agent-OK."}, "INBOX": {"TITLE": "Buat Kotak Masuk", "BODY": "Otentikasi akun <PERSON>a dan buat kotak masuk."}, "AGENT": {"TITLE": "Tambahkan Agen", "BODY": "Tambahkan agen ke kotak masuk yang dibuat."}, "FINISH": {"TITLE": "Voilà!", "BODY": "Sekarang kotak masuk Anda sudah siap!"}}, "ADD": {"CHANNEL_NAME": {"LABEL": "Nama Kotak Masuk", "PLACEHOLDER": "Masukkan nama kotak masuk anda (mis: Acme Inc)", "ERROR": "Harap masukkan nama inbox yang valid"}, "WEBSITE_NAME": {"LABEL": "Nama Website", "PLACEHOLDER": "Masukkan nama situs web Anda (misalnya: Acme Inc)"}, "FB": {"HELP": "NB: <PERSON><PERSON>, kami hanya mendapatkan akses ke pesan Halaman Anda. Pesan pribadi Anda tidak akan pernah bisa diakses oleh AI Agent-OK.", "CHOOSE_PAGE": "<PERSON><PERSON><PERSON>", "CHOOSE_PLACEHOLDER": "<PERSON><PERSON><PERSON> halaman dari daftar", "INBOX_NAME": "Nama Kotak Masuk", "ADD_NAME": "Tambahkan nama untuk kotak masuk Anda", "PICK_NAME": "Pick a Name for your Inbox", "PICK_A_VALUE": "<PERSON><PERSON><PERSON>"}, "TWITTER": {"HELP": "Untuk menambahkan profil Twitter Anda sebagai saluran, <PERSON>a perlu mengautentikasi Profil Twitter Anda dengan mengklik 'Masuk dengan Twitter' ", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> kesalahan saat menghubungkan ke Twitter, harap coba lagi", "TWEETS": {"ENABLE": "Buat percakapan dari Tweet yang disebutkan"}}, "WEBSITE_CHANNEL": {"TITLE": "Channel Website", "DESC": "Buat saluran untuk situs web Anda dan mulai dukung pelanggan Anda melalui widget di website.", "LOADING_MESSAGE": "Membuat Saluran Dukungan Website", "CHANNEL_AVATAR": {"LABEL": "Avatar Channel"}, "CHANNEL_WEBHOOK_URL": {"LABEL": "URL Webhook", "PLACEHOLDER": "Please enter your Webhook URL", "ERROR": "Harap masukkan URL yang valid"}, "CHANNEL_DOMAIN": {"LABEL": "Domain Website", "PLACEHOLDER": "Masukkan domain situs web Anda (misalnya: acme.com)"}, "CHANNEL_WELCOME_TITLE": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACEHOLDER": "Halo!"}, "CHANNEL_WELCOME_TAGLINE": {"LABEL": "Tagline Selamat Datang", "PLACEHOLDER": "<PERSON><PERSON> membuatnya mudah untuk terhubung dengan kami. <PERSON><PERSON> apa saja kepada kami, atau bagikan tanggapan Anda."}, "CHANNEL_GREETING_MESSAGE": {"LABEL": "Pesan sambutan channel", "PLACEHOLDER": "Acme Inc biasanya membalas dalam beberapa jam."}, "CHANNEL_GREETING_TOGGLE": {"LABEL": "Aktifkan sambutan channel", "HELP_TEXT": "Automatically send a greeting message when a new conversation is created.", "ENABLED": "Diaktifkan", "DISABLED": "<PERSON><PERSON><PERSON><PERSON>"}, "REPLY_TIME": {"TITLE": "<PERSON><PERSON> waktu <PERSON>", "IN_A_FEW_MINUTES": "<PERSON><PERSON> be<PERSON> menit", "IN_A_FEW_HOURS": "<PERSON><PERSON> be<PERSON> jam", "IN_A_DAY": "<PERSON><PERSON>", "HELP_TEXT": "<PERSON><PERSON><PERSON> balasan ini akan di<PERSON>pilkan di widget live chat"}, "WIDGET_COLOR": {"LABEL": "<PERSON><PERSON>", "PLACEHOLDER": "<PERSON><PERSON><PERSON> warna yang digunakan di widget"}, "SUBMIT_BUTTON": "Buat Kotak Masuk", "API": {"ERROR_MESSAGE": "<PERSON><PERSON> tidak dapat membuat saluran situs web, harap coba lagi"}}, "TWILIO": {"TITLE": "<PERSON><PERSON><PERSON> SMS/Saluran WhatsApp", "DESC": "Integrasikan <PERSON><PERSON> dan mulailah mendukung pelanggan Anda melalui SMS atau WhatsApp.", "ACCOUNT_SID": {"LABEL": "Account SID", "PLACEHOLDER": "<PERSON><PERSON>an masukkan SID Akun Twi<PERSON>", "ERROR": "Bagian ini diperlukan"}, "API_KEY": {"USE_API_KEY": "Use API Key Authentication", "LABEL": "API Key SID", "PLACEHOLDER": "Please enter your API Key SID", "ERROR": "Bagian ini diperlukan"}, "API_KEY_SECRET": {"LABEL": "API Key Secret", "PLACEHOLDER": "Please enter your API Key Secret", "ERROR": "Bagian ini diperlukan"}, "MESSAGING_SERVICE_SID": {"LABEL": "SID Layanan <PERSON>esan", "PLACEHOLDER": "Masukkan SID Layanan Pesan Twilio <PERSON>a", "ERROR": "Bagian ini diperlukan", "USE_MESSAGING_SERVICE": "<PERSON><PERSON><PERSON>"}, "CHANNEL_TYPE": {"LABEL": "Tipe Channel", "ERROR": "Harap pilih Tipe Channel Anda"}, "AUTH_TOKEN": {"LABEL": "<PERSON><PERSON>", "PLACEHOLDER": "<PERSON><PERSON> ma<PERSON>kkan <PERSON>", "ERROR": "Bagian ini diperlukan"}, "CHANNEL_NAME": {"LABEL": "Nama Kotak Masuk", "PLACEHOLDER": "Ma<PERSON>kkan nama kotak masuk", "ERROR": "Bagian ini diperlukan"}, "PHONE_NUMBER": {"LABEL": "Nomor Telepon", "PLACEHOLDER": "<PERSON><PERSON><PERSON> masukkan nomor telepon dari mana pesan akan dikirim.", "ERROR": "Berikan nomor telepon yang valid yang dimulai dengan tanda `+` dan tidak mengandung spasi."}, "API_CALLBACK": {"TITLE": "URL Callback", "SUBTITLE": "<PERSON><PERSON> harus mengkonfigurasi pesan URL callback di <PERSON><PERSON><PERSON> dengan URL yang disebutkan di sini."}, "SUBMIT_BUTTON": "Buat Channel Twilio", "API": {"ERROR_MESSAGE": "<PERSON><PERSON> tidak dapat mengautentikasi kredensial Twilio, harap coba lagi"}}, "SMS": {"TITLE": "Saluran SMS", "DESC": "<PERSON><PERSON><PERSON> mendukung pelanggan Anda melalui SMS.", "PROVIDERS": {"LABEL": "Penyedia API", "TWILIO": "<PERSON><PERSON><PERSON>", "BANDWIDTH": "Bandwidth"}, "API": {"ERROR_MESSAGE": "<PERSON><PERSON> tidak dapat menyimpan saluran SMS"}, "BANDWIDTH": {"ACCOUNT_ID": {"LABEL": "ID Akun", "PLACEHOLDER": "<PERSON>lakan masukkan ID Akun Bandwidth Anda", "ERROR": "Bagian ini diperlukan"}, "API_KEY": {"LABEL": "API Key", "PLACEHOLDER": "Please enter your Bandwidth API Key", "ERROR": "Bagian ini diperlukan"}, "API_SECRET": {"LABEL": "API Secret", "PLACEHOLDER": "Please enter your Bandwidth API Secret", "ERROR": "Bagian ini diperlukan"}, "APPLICATION_ID": {"LABEL": "ID Aplikasi", "PLACEHOLDER": "Silakan masukkan ID Aplikasi Bandwidth Anda", "ERROR": "Bagian ini diperlukan"}, "INBOX_NAME": {"LABEL": "Nama Kotak Masuk", "PLACEHOLDER": "<PERSON><PERSON> masukkan nama inbox", "ERROR": "Bagian ini diperlukan"}, "PHONE_NUMBER": {"LABEL": "Nomor Telpon", "PLACEHOLDER": "<PERSON><PERSON><PERSON> masukkan nomor telepon dari mana pesan akan dikirim.", "ERROR": "Berikan nomor telepon yang valid yang dimulai dengan tanda `+` dan tidak mengandung spasi."}, "SUBMIT_BUTTON": "Buat Saluran Bandwidth", "API": {"ERROR_MESSAGE": "<PERSON>mi tidak dapat mengautentikasi kredensial Bandwidth, harap coba lagi"}, "API_CALLBACK": {"TITLE": "URL Callback", "SUBTITLE": "<PERSON><PERSON> harus mengonfigurasi URL panggilan balik pesan di Bandwidth dengan URL yang disebutkan di sini."}}}, "WHATSAPP": {"TITLE": "<PERSON><PERSON><PERSON>", "DESC": "<PERSON><PERSON><PERSON> mendukung pelanggan Anda melalui WhatsApp.", "PROVIDERS": {"LABEL": "Penyedia API", "TWILIO": "<PERSON><PERSON><PERSON>", "WHATSAPP_CLOUD": "WhatsApp Cloud", "360_DIALOG": "360Dialog"}, "INBOX_NAME": {"LABEL": "Nama Kotak Masuk", "PLACEHOLDER": "Ma<PERSON>kkan nama kotak masuk", "ERROR": "Bagian ini diperlukan"}, "PHONE_NUMBER": {"LABEL": "Nomor Telpon", "PLACEHOLDER": "<PERSON><PERSON><PERSON> masukkan nomor telepon dari mana pesan akan dikirim.", "ERROR": "Berikan nomor telepon yang valid yang dimulai dengan tanda `+` dan tidak mengandung spasi."}, "PHONE_NUMBER_ID": {"LABEL": "ID nomor telepon", "PLACEHOLDER": "<PERSON>lakan masukkan ID nomor Telepon yang diperoleh dari dasbor pengembang Facebook.", "ERROR": "Harap masukkan nilai yang valid."}, "BUSINESS_ACCOUNT_ID": {"LABEL": "ID Akun <PERSON>", "PLACEHOLDER": "<PERSON><PERSON><PERSON> masukkan ID Akun B<PERSON> yang diperoleh dari dasbor pengembang Facebook.", "ERROR": "Harap masukkan nilai yang valid."}, "WEBHOOK_VERIFY_TOKEN": {"LABEL": "Token Verifikasi Webhook", "PLACEHOLDER": "Enter a verify token which you want to configure for Facebook webhooks.", "ERROR": "Harap masukkan nilai yang valid."}, "API_KEY": {"LABEL": "API Key", "SUBTITLE": "Konfigurasikan kunci WhatsApp API.", "PLACEHOLDER": "API Key", "ERROR": "Harap masukkan nilai yang valid."}, "API_CALLBACK": {"TITLE": "URL Callback", "SUBTITLE": "Anda harus mengonfigurasi URL webhook dan token verifikasi di portal Pengembang Facebook dengan nilai yang ditunjukkan di bawah ini.", "WEBHOOK_URL": "URL Webhook", "WEBHOOK_VERIFICATION_TOKEN": "Token Verifikasi Webhook"}, "SUBMIT_BUTTON": "Buat Saluran WhatsApp", "API": {"ERROR_MESSAGE": "<PERSON>mi tidak dapat menyimpan saluran WhatsApp"}}, "API_CHANNEL": {"TITLE": "API Channel", "DESC": "Integrasikan dengan saluran API dan mulai dukung pelanggan Anda.", "CHANNEL_NAME": {"LABEL": "Nama Channel", "PLACEHOLDER": "Harap masukkan nama channel", "ERROR": "Bagian ini diperlukan"}, "WEBHOOK_URL": {"LABEL": "URL Webhook", "SUBTITLE": "Configure the URL where you want to receive callbacks on events.", "PLACEHOLDER": "URL Webhook"}, "SUBMIT_BUTTON": "Buat Channel API", "API": {"ERROR_MESSAGE": "Kami tidak dapat menyimpan channel api"}}, "EMAIL_CHANNEL": {"TITLE": "Channel Email", "DESC": "Integrate your email inbox.", "CHANNEL_NAME": {"LABEL": "Nama Channel", "PLACEHOLDER": "Harap masukkan nama channel", "ERROR": "Bagian ini diperlukan"}, "EMAIL": {"LABEL": "Email", "SUBTITLE": "<PERSON><PERSON> email ke tempat pelanggan Anda mengirimkan tiket dukungan", "PLACEHOLDER": "Email"}, "SUBMIT_BUTTON": "Buat Channel Email", "API": {"ERROR_MESSAGE": "Ka<PERSON> tidak dapat menyimpan channel email"}, "FINISH_MESSAGE": "<PERSON><PERSON><PERSON> email <PERSON><PERSON> ke alamat email berikut."}, "LINE_CHANNEL": {"TITLE": "Channel LINE", "DESC": "Integrasikan dengan channel LINE dan mulai mendukung pelanggan Anda.", "CHANNEL_NAME": {"LABEL": "Nama Channel", "PLACEHOLDER": "Harap masukkan nama channel", "ERROR": "Bagian ini diperlukan"}, "LINE_CHANNEL_ID": {"LABEL": "Channel ID LINE", "PLACEHOLDER": "Channel ID LINE"}, "LINE_CHANNEL_SECRET": {"LABEL": "LINE Channel Secret", "PLACEHOLDER": "LINE Channel Secret"}, "LINE_CHANNEL_TOKEN": {"LABEL": "Token Channel LINE", "PLACEHOLDER": "Token Channel LINE"}, "SUBMIT_BUTTON": "Buat channel LINE", "API": {"ERROR_MESSAGE": "Kami tidak dapat menyimpan channel LINE"}, "API_CALLBACK": {"TITLE": "URL Callback", "SUBTITLE": "Anda harus mengatur webhook URL pada aplikasi LINE dengan URL yang tercantum disini."}}, "TELEGRAM_CHANNEL": {"TITLE": "Channel Telegram", "DESC": "Integrasikan dengan channel Telegram dan mulai mendukung pelanggan Anda.", "BOT_TOKEN": {"LABEL": "Token <PERSON>", "SUBTITLE": "Konfigurasikan token bot yang <PERSON><PERSON> peroleh dari Telegram BotFather.", "PLACEHOLDER": "Token <PERSON>"}, "SUBMIT_BUTTON": "Buat Channel Telegram", "API": {"ERROR_MESSAGE": "Kami tidak dapat menyimpan channel telegram"}}, "AUTH": {"TITLE": "Pilih sebuah channel", "DESC": "AI Agent-OK supports live-chat widgets, Facebook Messenger, Twitter profiles, WhatsApp, Emails, etc., as channels. If you want to build a custom channel, you can create it using the API channel. To get started, choose one of the channels below."}, "AGENTS": {"TITLE": "Agen", "DESC": "Di sini Anda dapat menambahkan agen untuk mengelola kotak masuk yang baru Anda buat. <PERSON>ya agen terpilih ini yang akan memiliki akses ke kotak masuk Anda. Agen yang bukan bagian dari kotak masuk ini tidak akan dapat melihat atau menanggapi pesan di kotak masuk ini saat mereka masuk. <br> <b> Keterangan: </b> Sebagai administrator, jika Anda memerlukan akses ke semua kotak masuk, <PERSON>a harus menambahkan diri Anda sebagai agen ke semua kotak masuk yang Anda buat.", "VALIDATION_ERROR": "Add at least one agent to your new Inbox", "PICK_AGENTS": "<PERSON><PERSON><PERSON> agen untuk kotak masuk"}, "DETAILS": {"TITLE": "Detail Kotak Masuk", "DESC": "<PERSON><PERSON> dropdown di bawah, <PERSON><PERSON><PERSON> Facebook yang ingin Anda hubungkan ke AI Agent-OK. Anda juga dapat memberikan nama khusus ke kotak masuk Anda untuk identifikasi yang lebih baik."}, "FINISH": {"TITLE": "Ber<PERSON>il!", "DESC": "Anda telah berhasil menyelesaikan integrasi Halaman Facebook Anda dengan AI Agent-OK. Saat pelanggan mengirim pesan ke Halaman Anda, percakapan akan otomatis muncul di kotak masuk Anda. <br> Kami juga menyediakan script widget yang dapat dengan mudah ditambahkan ke website Anda. Setelah ini aktif di website Anda, pelanggan dapat mengirimi Anda pesan langsung dari website Anda tanpa bantuan alat eksternal dan percakapan akan muncul di sini, di AI Agent-OK. <br> <PERSON><PERSON>, ya? Ya, kami pasti berusaha untuk :)"}, "EMAIL_PROVIDER": {"TITLE": "<PERSON><PERSON><PERSON> penyedia email Anda", "DESCRIPTION": "<PERSON><PERSON>h penyedia email dari daftar di bawah ini. Ji<PERSON> Anda tidak melihat penyedia email <PERSON><PERSON> dalam daftar, <PERSON>a dapat memilih opsi penyedia lain dan memberikan Kredensial IMAP dan SMTP."}, "MICROSOFT": {"TITLE": "Microsoft Email", "DESCRIPTION": "Klik tombol Masuk dengan Microsoft untuk memulai. Anda akan dialihkan ke halaman masuk email. Setelah Anda menerima izin yang diminta, <PERSON>a akan diarahkan kembali ke langkah pembuatan kotak masuk.", "EMAIL_PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON> email", "SIGN_IN": "Sign in with Microsoft", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat menghub<PERSON>kan ke Microsoft, harap coba lagi"}, "GOOGLE": {"TITLE": "Google Email", "DESCRIPTION": "Click on the Sign in with Google button to get started. You will redirected to the email sign in page. Once you accept the requested permissions, you would be redirected back to the inbox creation step.", "SIGN_IN": "Sign in with Google", "EMAIL_PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON> email", "ERROR_MESSAGE": "There was an error connecting to Google, please try again"}}, "DETAILS": {"LOADING_FB": "Mengautentikasi Anda dengan Facebook...", "ERROR_FB_LOADING": "Error loading Facebook SDK. Please disable any ad-blockers and try again from a different browser.", "ERROR_FB_AUTH": "<PERSON> yang tidak beres, <PERSON><PERSON> refresh halaman...", "ERROR_FB_UNAUTHORIZED": "You're not authorized to perform this action. ", "ERROR_FB_UNAUTHORIZED_HELP": "Please ensure you have access to the Facebook page with full control. You can read more about Facebook roles <a href=\" https://www.facebook.com/help/187316341316631\">here</a>.", "CREATING_CHANNEL": "Membuat kotak masuk Anda...", "TITLE": "Konfigurasi Detail Kotak Masuk", "DESC": ""}, "AGENTS": {"BUTTON_TEXT": "Tambahkan Agen", "ADD_AGENTS": "Menambahkan Agen ke Kotak Masuk Anda..."}, "FINISH": {"TITLE": "Kotak Masuk anda sudah siap!", "MESSAGE": "<PERSON>a sekarang dapat menangani pelanggan Anda melalui Channel baru Anda. Selamat mendukung", "BUTTON_TEXT": "Pergi ke Kotak Masuk", "MORE_SETTINGS": "Pen<PERSON><PERSON><PERSON> lebih lengkap", "WEBSITE_SUCCESS": "Anda telah berhasil menyelesaikan pembuatan channel website. <PERSON>in kode yang ditunjukkan di bawah ini dan tempelkan di website Anda. Saat pelanggan menggunakan live chat, percakapan tersebut secara otomatis akan muncul di kotak masuk Anda."}, "REAUTH": "<PERSON><PERSON><PERSON><PERSON> ulang", "VIEW": "Lihat", "EDIT": {"API": {"SUCCESS_MESSAGE": "Pengaturan kotak masuk ber<PERSON>il diperbarui", "AUTO_ASSIGNMENT_SUCCESS_MESSAGE": "<PERSON><PERSON><PERSON> otomati<PERSON> be<PERSON>", "ERROR_MESSAGE": "We couldn't update inbox settings. Please try again later."}, "EMAIL_COLLECT_BOX": {"ENABLED": "Diaktifkan", "DISABLED": "<PERSON><PERSON><PERSON><PERSON>"}, "ENABLE_CSAT": {"ENABLED": "Diaktifkan", "DISABLED": "<PERSON><PERSON><PERSON><PERSON>"}, "SENDER_NAME_SECTION": {"TITLE": "Sender name", "SUB_TEXT": "Select the name shown to your customer when they receive emails from your agents.", "FOR_EG": "For eg:", "FRIENDLY": {"TITLE": "<PERSON><PERSON>", "FROM": "dari", "SUBTITLE": "Add the name of the agent who sent the reply in the sender name to make it friendly."}, "PROFESSIONAL": {"TITLE": "Profesional", "SUBTITLE": "Use only the configured business name as the sender name in the email header."}, "BUSINESS_NAME": {"BUTTON_TEXT": "+ Configure your business name", "PLACEHOLDER": "Enter your business name", "SAVE_BUTTON_TEXT": "Simpan"}}, "ALLOW_MESSAGES_AFTER_RESOLVED": {"ENABLED": "Diaktifkan", "DISABLED": "<PERSON><PERSON><PERSON><PERSON>"}, "ENABLE_CONTINUITY_VIA_EMAIL": {"ENABLED": "Diaktifkan", "DISABLED": "<PERSON><PERSON><PERSON><PERSON>"}, "LOCK_TO_SINGLE_CONVERSATION": {"ENABLED": "Diaktifkan", "DISABLED": "<PERSON><PERSON><PERSON><PERSON>"}, "ENABLE_HMAC": {"LABEL": "Aktifkan"}}, "DELETE": {"BUTTON_TEXT": "Hapus", "AVATAR_DELETE_BUTTON_TEXT": "Hapus Avatar", "CONFIRM": {"TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MESSAGE": "<PERSON><PERSON><PERSON><PERSON> Anda yakin untuk menghapus ", "PLACE_HOLDER": "<PERSON><PERSON>an ketik {inboxName} untuk konfirmasi", "YES": "Ya, Hapus ", "NO": "Tidak, Simpan "}, "API": {"SUCCESS_MESSAGE": "Kotak Masuk ber<PERSON>", "ERROR_MESSAGE": "Tidak dapat menghapus kotak masuk. <PERSON>lakan coba lagi nanti.", "AVATAR_SUCCESS_MESSAGE": "Avatar kotak masuk ber<PERSON><PERSON>", "AVATAR_ERROR_MESSAGE": "Tidak dapat menghapus avatar kotak masuk. <PERSON>lakan coba lagi nanti."}}, "TABS": {"SETTINGS": "<PERSON><PERSON><PERSON><PERSON>", "COLLABORATORS": "Kolaborator", "CONFIGURATION": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CAMPAIGN": "Kampanye", "PRE_CHAT_FORM": "Formulir <PERSON>", "BUSINESS_HOURS": "<PERSON>", "WIDGET_BUILDER": "Pembuat Widget", "BOT_CONFIGURATION": "Kon<PERSON><PERSON><PERSON><PERSON>"}, "SETTINGS": "<PERSON><PERSON><PERSON><PERSON>", "FEATURES": {"LABEL": "<PERSON><PERSON>", "DISPLAY_FILE_PICKER": "Tampilkan file picker di widget", "DISPLAY_EMOJI_PICKER": "<PERSON><PERSON><PERSON><PERSON> pilihan emoji di widget", "ALLOW_END_CONVERSATION": "Izinkan pengguna mengakhiri percakapan dari widget", "USE_INBOX_AVATAR_FOR_BOT": "Gunakan nama kotak masuk dan avatar untuk bot"}, "SETTINGS_POPUP": {"MESSENGER_HEADING": "<PERSON>", "MESSENGER_SUB_HEAD": "Tempatkan tombol ini di dalam tag <body> <PERSON><PERSON>", "INBOX_AGENTS": "Agen", "INBOX_AGENTS_SUB_TEXT": "Tambahkan atau hapus agen dari kotak masuk ini", "AGENT_ASSIGNMENT": "Tugas Percakapan", "AGENT_ASSIGNMENT_SUB_TEXT": "<PERSON><PERSON><PERSON> setelan tugas perca<PERSON>pan", "UPDATE": "<PERSON><PERSON><PERSON>", "ENABLE_EMAIL_COLLECT_BOX": "Aktifkan kotak pengumpulan email", "ENABLE_EMAIL_COLLECT_BOX_SUB_TEXT": "Aktifkan atau nonaktifkan kotak pengumpulan email pada percakpaan baru", "AUTO_ASSIGNMENT": "Aktifkan penugasan otomatis", "ENABLE_CSAT": "Aktifkan CSAT", "SENDER_NAME_SECTION": "Enable Agent Name in Email", "ENABLE_CSAT_SUB_TEXT": "Aktifkan/Nonaktifkan survey CSAT (Kepuasan pelanggan) setelah penyelesaian percakapan", "SENDER_NAME_SECTION_TEXT": "Enable/Disable showing Agent's name in email, if disabled it will show business name", "ENABLE_CONTINUITY_VIA_EMAIL": "Aktifkan kontinuitas percakapan melalui email", "ENABLE_CONTINUITY_VIA_EMAIL_SUB_TEXT": "<PERSON>caka<PERSON> akan berl<PERSON>t melalui email jika alamat email kontak tersedia.", "LOCK_TO_SINGLE_CONVERSATION": "Kunci ke satu percakapan", "LOCK_TO_SINGLE_CONVERSATION_SUB_TEXT": "Aktifkan atau nonaktifkan beberapa percakapan untuk kontak yang sama di kotak masuk ini", "INBOX_UPDATE_TITLE": "Pengaturan Kotak Masuk", "INBOX_UPDATE_SUB_TEXT": "<PERSON><PERSON><PERSON> pengaturan kotak masuk And<PERSON>", "AUTO_ASSIGNMENT_SUB_TEXT": "Mengaktifkan atau menonaktifkan penugasan otomatis percakapan baru ke agen yang ditambahkan ke kotak masuk ini.", "HMAC_VERIFICATION": "Validasi Identitas Pengguna", "HMAC_DESCRIPTION": "In order to validate the user's identity, you can pass an `identifier_hash` for each user. You can generate a HMAC sha256 hash using the `identifier` with the key shown here.", "HMAC_LINK_TO_DOCS": "Anda dapat membaca lebih lanjut di sini.", "HMAC_MANDATORY_VERIFICATION": "Terapkan Validasi Identitas Pengguna", "HMAC_MANDATORY_DESCRIPTION": "If enabled, requests missing the `identifier_hash` will be rejected.", "INBOX_IDENTIFIER": "Pengenal Kotak Masuk", "INBOX_IDENTIFIER_SUB_TEXT": "Gunakan token inbox_identifier` yang terlihat disini untuk mengautentikasi klien API Anda.", "FORWARD_EMAIL_TITLE": "Teruskan ke Email", "FORWARD_EMAIL_SUB_TEXT": "<PERSON><PERSON><PERSON> email <PERSON><PERSON> ke alamat email berikut.", "ALLOW_MESSAGES_AFTER_RESOLVED": "Izinkan pesan setelah percakapan diselesaikan", "ALLOW_MESSAGES_AFTER_RESOLVED_SUB_TEXT": "Izinkan pengguna akhir mengirim pesan bahkan setelah percakapan diselesaikan.", "WHATSAPP_SECTION_SUBHEADER": "Kunci API ini digunakan untuk integrasi dengan API WhatsApp.", "WHATSAPP_SECTION_UPDATE_SUBHEADER": "Enter the new API key to be used for the integration with the WhatsApp APIs.", "WHATSAPP_SECTION_TITLE": "API Key", "WHATSAPP_SECTION_UPDATE_TITLE": "Perbarui <PERSON> API", "WHATSAPP_SECTION_UPDATE_PLACEHOLDER": "Masukkan Kunci API baru di sini", "WHATSAPP_SECTION_UPDATE_BUTTON": "<PERSON><PERSON><PERSON>", "WHATSAPP_WEBHOOK_TITLE": "Token Verifikasi Webhook", "WHATSAPP_WEBHOOK_SUBHEADER": "Token ini digunakan untuk memverifikasi keaslian titik akhir webhook.", "UPDATE_PRE_CHAT_FORM_SETTINGS": "<PERSON><PERSON><PERSON>uli<PERSON>"}, "HELP_CENTER": {"LABEL": "Pusat <PERSON>", "PLACEHOLDER": "<PERSON><PERSON><PERSON>", "SELECT_PLACEHOLDER": "<PERSON><PERSON><PERSON>", "REMOVE": "Hapus Pusat Bantuan", "SUB_TEXT": "Lampirkan Pusat Bantuan dengan kotak masuk"}, "AUTO_ASSIGNMENT": {"MAX_ASSIGNMENT_LIMIT": "<PERSON>as <PERSON> otomatis", "MAX_ASSIGNMENT_LIMIT_RANGE_ERROR": "Harap masukkan nilai yang lebih besar dari 0", "MAX_ASSIGNMENT_LIMIT_SUB_TEXT": "Batasi jumlah maksimum percakapan dari kotak masuk ini yang dapat ditetapkan secara otomatis ke agen"}, "FACEBOOK_REAUTHORIZE": {"TITLE": "<PERSON><PERSON><PERSON><PERSON> ulang", "SUBTITLE": "Koneksi Facebook Anda telah kedal<PERSON>, hubungkan kembali halaman Facebook Anda untuk melanjutkan layanan", "MESSAGE_SUCCESS": "<PERSON><PERSON><PERSON><PERSON> kembali", "MESSAGE_ERROR": "<PERSON><PERSON><PERSON><PERSON>, harap coba lagi"}, "PRE_CHAT_FORM": {"DESCRIPTION": "Formulir pra-obrolan memungkinkan Anda untuk menangkap informasi pengguna sebelum mereka memulai percakapan dengan <PERSON>.", "SET_FIELDS": "Bidang formulir pra obrolan", "SET_FIELDS_HEADER": {"FIELDS": "Bidang", "LABEL": "Label", "PLACE_HOLDER": "Placeholder", "KEY": "<PERSON><PERSON><PERSON>", "TYPE": "Tipe", "REQUIRED": "<PERSON><PERSON><PERSON><PERSON>"}, "ENABLE": {"LABEL": "Aktifkan formulir pra o<PERSON>lan", "OPTIONS": {"ENABLED": "Ya", "DISABLED": "Tidak"}}, "PRE_CHAT_MESSAGE": {"LABEL": "Pesan pra obrolan", "PLACEHOLDER": "Pesan ini akan terlihat oleh pengguna bersama dengan formulir"}, "REQUIRE_EMAIL": {"LABEL": "Pengunjung harus memberikan nama dan alamat email mereka sebelum memulai obrolan"}}, "BUSINESS_HOURS": {"TITLE": "<PERSON><PERSON> keter<PERSON><PERSON>", "SUBTITLE": "<PERSON><PERSON> ketersediaan <PERSON>a di widget livechat", "WEEKLY_TITLE": "Tetapkan jam mingguan Anda", "TIMEZONE_LABEL": "<PERSON><PERSON>h zona waktu", "UPDATE": "<PERSON><PERSON><PERSON> pengaturan jam kerja", "TOGGLE_AVAILABILITY": "Aktifkan ketersediaan bisnis untuk kotak masuk ini", "UNAVAILABLE_MESSAGE_LABEL": "Pesan tidak tersedia untuk pengunjung", "TOGGLE_HELP": "Enabling business availability will show the available hours on live chat widget even if all the agents are offline. Outside available hours visitors can be warned with a message and a pre-chat form.", "DAY": {"ENABLE": "Aktifkan ketersediaan untuk hari ini", "UNAVAILABLE": "Tidak tersedia", "HOURS": "jam", "VALIDATION_ERROR": "<PERSON><PERSON><PERSON> mulai harus sebelum waktu tutup.", "CHOOSE": "<PERSON><PERSON><PERSON>"}, "ALL_DAY": "Sepanjang hari"}, "IMAP": {"TITLE": "IMAP", "SUBTITLE": "Tetapkan detail IMAP Anda", "NOTE_TEXT": "Untuk mengaktifkan SMTP, konfigurasikan IMAP.", "UPDATE": "<PERSON><PERSON><PERSON> setelan IMAP", "TOGGLE_AVAILABILITY": "Aktifkan konfigurasi IMAP untuk kotak masuk ini", "TOGGLE_HELP": "Enabling IMAP will help the user to receive email", "EDIT": {"SUCCESS_MESSAGE": "Setelan IMAP berhasil diperbarui", "ERROR_MESSAGE": "Tidak dapat memperbarui setelan IMAP"}, "ADDRESS": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACE_HOLDER": "<PERSON><PERSON><PERSON> (Misalnya: imap.gmail.com)"}, "PORT": {"LABEL": "Port", "PLACE_HOLDER": "Port"}, "LOGIN": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACE_HOLDER": "<PERSON><PERSON><PERSON>"}, "PASSWORD": {"LABEL": "<PERSON><PERSON>", "PLACE_HOLDER": "<PERSON><PERSON>"}, "ENABLE_SSL": "Aktifkan SSL"}, "MICROSOFT": {"TITLE": "Microsoft", "SUBTITLE": "Otorisasi ulang akun MICROSOFT Anda"}, "SMTP": {"TITLE": "SMTP", "SUBTITLE": "Tetapkan detail SMTP Anda", "UPDATE": "<PERSON><PERSON><PERSON> setelan SMTP", "TOGGLE_AVAILABILITY": "Aktifkan konfigurasi SMTP untuk kotak masuk ini", "TOGGLE_HELP": "Mengaktifkan SMTP akan membantu pengguna mengirim email", "EDIT": {"SUCCESS_MESSAGE": "Pengaturan SMTP berhasil diperbarui", "ERROR_MESSAGE": "Tidak dapat memperbarui setelan SMTP"}, "ADDRESS": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACE_HOLDER": "<PERSON><PERSON><PERSON> (Misalnya: smtp.gmail.com)"}, "PORT": {"LABEL": "Port", "PLACE_HOLDER": "Port"}, "LOGIN": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACE_HOLDER": "<PERSON><PERSON><PERSON>"}, "PASSWORD": {"LABEL": "<PERSON><PERSON>", "PLACE_HOLDER": "<PERSON><PERSON>"}, "DOMAIN": {"LABEL": "Domain", "PLACE_HOLDER": "Domain"}, "ENCRYPTION": "<PERSON><PERSON><PERSON><PERSON>", "SSL_TLS": "SSL/TLS", "START_TLS": "STARTTLS", "OPEN_SSL_VERIFY_MODE": "Mode Verifikasi Open SSL", "AUTH_MECHANISM": "Autentikasi"}, "NOTE": "Catatan: ", "WIDGET_BUILDER": {"WIDGET_OPTIONS": {"AVATAR": {"LABEL": "Website Avatar", "DELETE": {"API": {"SUCCESS_MESSAGE": "Ava<PERSON> ber<PERSON><PERSON>", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON><PERSON>, harap coba lagi"}}}, "WEBSITE_NAME": {"LABEL": "Nama Website", "PLACE_HOLDER": "Masukkan nama situs web Anda (misalnya: Acme Inc)", "ERROR": "Masukkan nama situs web yang valid"}, "WELCOME_HEADING": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACE_HOLDER": "Hai, yang di sana!"}, "WELCOME_TAGLINE": {"LABEL": "Tagline Selamat Datang", "PLACE_HOLDER": "<PERSON><PERSON> membuatnya mudah untuk terhubung dengan kami. <PERSON><PERSON> apa saja kepada kami, atau bagikan tanggapan Anda."}, "REPLY_TIME": {"LABEL": "<PERSON><PERSON><PERSON>", "IN_A_FEW_MINUTES": "<PERSON><PERSON> be<PERSON> menit", "IN_A_FEW_HOURS": "<PERSON><PERSON> be<PERSON> jam", "IN_A_DAY": "<PERSON><PERSON>"}, "WIDGET_COLOR_LABEL": "<PERSON><PERSON>", "WIDGET_BUBBLE_POSITION_LABEL": "Posisi Widget Gelembung", "WIDGET_BUBBLE_TYPE_LABEL": "<PERSON><PERSON> W<PERSON>", "WIDGET_BUBBLE_LAUNCHER_TITLE": {"DEFAULT": "<PERSON>t dengan kami", "LABEL": "<PERSON><PERSON><PERSON> W<PERSON>t Gelembung Launcher", "PLACE_HOLDER": "<PERSON>t dengan kami"}, "UPDATE": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON> W<PERSON>t", "API": {"SUCCESS_MESSAGE": "Pengaturan widget ber<PERSON><PERSON> diperbarui", "ERROR_MESSAGE": "Tidak dapat memperbarui setelan widget"}}, "WIDGET_VIEW_OPTION": {"PREVIEW": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SCRIPT": "<PERSON><PERSON><PERSON>"}, "WIDGET_BUBBLE_POSITION": {"LEFT": "<PERSON><PERSON>", "RIGHT": "<PERSON><PERSON>"}, "WIDGET_BUBBLE_TYPE": {"STANDARD": "<PERSON>ar", "EXPANDED_BUBBLE": "Gelembung yang Diluaskan"}}, "WIDGET_SCREEN": {"DEFAULT": "<PERSON><PERSON><PERSON>", "CHAT": "Cha<PERSON>"}, "REPLY_TIME": {"IN_A_FEW_MINUTES": "<PERSON><PERSON><PERSON><PERSON> membalas dalam beberapa menit", "IN_A_FEW_HOURS": "<PERSON><PERSON><PERSON>a membalas dalam beberapa jam", "IN_A_DAY": "<PERSON><PERSON><PERSON><PERSON> memba<PERSON> dalam sehari"}, "FOOTER": {"START_CONVERSATION_BUTTON_TEXT": "<PERSON><PERSON>", "CHAT_INPUT_PLACEHOLDER": "Ketik pesan Anda"}, "BODY": {"TEAM_AVAILABILITY": {"ONLINE": "<PERSON><PERSON> online", "OFFLINE": "<PERSON>mi <PERSON>g tidak tersedia saat ini"}, "USER_MESSAGE": "Hi", "AGENT_MESSAGE": "Hall<PERSON>"}, "BRANDING_TEXT": "Powered by AI Agent-<PERSON>", "SCRIPT_SETTINGS": "\n      window.AI Agent-OKSettings = {options};"}, "EMAIL_PROVIDERS": {"MICROSOFT": "Microsoft", "GOOGLE": "Google", "OTHER_PROVIDERS": "<PERSON><PERSON><PERSON>"}, "CHANNELS": {"MESSENGER": "<PERSON>", "WEB_WIDGET": "Website", "TWITTER_PROFILE": "Twitter", "TWILIO_SMS": "<PERSON><PERSON><PERSON>", "WHATSAPP": "WhatsApp", "SMS": "SMS", "EMAIL": "Email", "TELEGRAM": "Telegram", "LINE": "Line", "API": "API Channel"}}}