{"INTEGRATION_SETTINGS": {"SHOPIFY": {"DELETE": {"TITLE": "Delete Shopify Integration", "MESSAGE": "Are you sure you want to delete the Shopify integration?"}, "STORE_URL": {"TITLE": "Connect Shopify Store", "LABEL": "Store URL", "PLACEHOLDER": "your-store.myshopify.com", "HELP": "Enter your Shopify store's myshopify.com URL", "CANCEL": "Batalkan", "SUBMIT": "Connect Store"}, "ERROR": "There was an error connecting to Shopify. Please try again or contact support if the issue persists."}, "HEADER": "Integrasi", "DESCRIPTION": "AI Agent-OK integrates with multiple tools and services to improve your team's efficiency. Explore the list below to configure your favorite apps.", "LEARN_MORE": "Learn more about integrations", "LOADING": "Fetching integrations", "CAPTAIN": {"DISABLED": "Captain is not enabled on your account.", "CLICK_HERE_TO_CONFIGURE": "Click here to configure", "LOADING_CONSOLE": "Loading Captain <PERSON><PERSON><PERSON>...", "FAILED_TO_LOAD_CONSOLE": "Failed to load Captain <PERSON><PERSON>. Please refresh and try again."}, "WEBHOOK": {"SUBSCRIBED_EVENTS": "<PERSON><PERSON><PERSON>", "LEARN_MORE": "Learn more about webhooks", "FORM": {"CANCEL": "Batalkan", "DESC": "Acara Webhook memberi Anda informasi realtime tentang apa yang terjadi di akun AI Agent-OK Anda. Harap masukkan URL yang valid untuk mengkonfigurasi callback.", "SUBSCRIPTIONS": {"LABEL": "<PERSON>car<PERSON>", "EVENTS": {"CONVERSATION_CREATED": "Percakapan Dibuat", "CONVERSATION_STATUS_CHANGED": "Perubahan Status Percakapan", "CONVERSATION_UPDATED": "<PERSON><PERSON><PERSON><PERSON>", "MESSAGE_CREATED": "Pesan dibuat", "MESSAGE_UPDATED": "<PERSON><PERSON>", "WEBWIDGET_TRIGGERED": "Widget obrolan langsung dibuka oleh pengguna", "CONTACT_CREATED": "Kontak dibuat", "CONTACT_UPDATED": "Kontak diperbarui"}}, "END_POINT": {"LABEL": "URL Webhook", "PLACEHOLDER": "Example: {webhookExampleURL}", "ERROR": "Harap masukkan URL yang valid"}, "EDIT_SUBMIT": "<PERSON><PERSON><PERSON> webhook", "ADD_SUBMIT": "Tambah Webhook"}, "TITLE": "Webhook", "CONFIGURE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HEADER": "<PERSON><PERSON><PERSON><PERSON>hook", "HEADER_BTN_TXT": "Tambah webhook baru", "LOADING": "Mengambil webhook terlampir", "SEARCH_404": "Tidak ada item yang cocok dengan kueri ini", "SIDEBAR_TXT": "<p><b>Webhook</b> </p> <p>Webhook adalah callback HTTP yang dapat ditentukan untuk setiap akun. Mereka dipicu oleh peristiwa seperti pembuatan pesan di AI Agent-OK. Anda dapat menambah lebih dari satu webhook untuk akun ini. <br /><br /> Untuk menambahkan <b>webhook</b>, klik tombol <b>Tambahkan webhook baru</b>. Anda juga dapat menghapus webhook yang ada dengan mengklik tombol Hapus.</p>", "LIST": {"404": "Tidak ada webhook yang dikonfigurasi untuk akun ini.", "TITLE": "<PERSON><PERSON><PERSON>", "TABLE_HEADER": {"WEBHOOK_ENDPOINT": "Endpoint webhook", "ACTIONS": "<PERSON><PERSON><PERSON>"}}, "EDIT": {"BUTTON_TEXT": "Edit", "TITLE": "Sunting webhook", "API": {"SUCCESS_MESSAGE": "Konfigurasi webhook ber<PERSON><PERSON>er<PERSON>ui", "ERROR_MESSAGE": "Tidak dapat terhubung ke Server Woot, Silahkan coba lagi nanti"}}, "ADD": {"CANCEL": "Batalkan", "TITLE": "Tambah webhook baru", "API": {"SUCCESS_MESSAGE": "Kon<PERSON>gu<PERSON>i webhook ber<PERSON><PERSON> di<PERSON>", "ERROR_MESSAGE": "Tidak dapat terhubung ke Server Woot, Silahkan coba lagi nanti"}}, "DELETE": {"BUTTON_TEXT": "Hapus", "API": {"SUCCESS_MESSAGE": "Webhook ber<PERSON><PERSON>", "ERROR_MESSAGE": "Tidak dapat terhubung ke Server Woot, Silahkan coba lagi nanti"}, "CONFIRM": {"TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MESSAGE": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus webhook? ({webhookURL})", "YES": "Ya, Hapus ", "NO": "Tidak, Simpan"}}}, "SLACK": {"DELETE": "Hapus", "DELETE_CONFIRMATION": {"TITLE": "Delete the integration", "MESSAGE": "Are you sure you want to delete the integration? Doing so will result in the loss of access to conversations on your Slack workspace."}, "HELP_TEXT": {"TITLE": "Using Slack Integration", "BODY": "With this integration, all of your incoming conversations will be synced to the ***{selectedChannel<PERSON><PERSON>}*** channel in your Slack workspace. You can manage all your customer conversations right within the channel and never miss a message.\n\nHere are the main features of the integration:\n\n**Respond to conversations from within Slack:** To respond to a conversation in the ***{selectedChannelName}*** Slack channel, simply type out your message and send it as a thread. This will create a response back to the customer through AI Agent-OK. It's that simple!\n\n **Create private notes:** If you want to create private notes instead of replies, start your message with ***`note:`***. This ensures that your message is kept private and won't be visible to the customer.\n\n**Associate an agent profile:** If the person who replied on Slack has an agent profile in AI Agent-OK under the same email, the replies will be associated with that agent profile automatically. This means you can easily track who said what and when. On the other hand, when the replier doesn't have an associated agent profile, the replies will appear from the bot profile to the customer.", "SELECTED": "selected"}, "SELECT_CHANNEL": {"OPTION_LABEL": "Select a channel", "UPDATE": "<PERSON><PERSON><PERSON>", "BUTTON_TEXT": "Connect channel", "DESCRIPTION": "Your Slack workspace is now linked with AI Agent-OK. However, the integration is currently inactive. To activate the integration and connect a channel to AI Agent-OK, please click the button below.\n\n**Note:** If you are attempting to connect a private channel, add the AI Agent-OK app to the Slack channel before proceeding with this step.", "ATTENTION_REQUIRED": "Attention required", "EXPIRED": "Your Slack integration has expired. To continue receiving messages on Slack, please delete the integration and connect your workspace again."}, "UPDATE_ERROR": "There was an error updating the integration, please try again", "UPDATE_SUCCESS": "The channel is connected successfully", "FAILED_TO_FETCH_CHANNELS": "There was an error fetching the channels from Slack, please try again"}, "DYTE": {"CLICK_HERE_TO_JOIN": "Klik di sini untuk bergabung", "LEAVE_THE_ROOM": "Tinggalkan ruangan", "START_VIDEO_CALL_HELP_TEXT": "<PERSON><PERSON> video baru dengan pelanggan", "JOIN_ERROR": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat bergabung dengan panggilan, harap coba lagi", "CREATE_ERROR": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuat tautan pertemuan, harap coba lagi"}, "OPEN_AI": {"AI_ASSIST": "AI Assist", "WITH_AI": " {option} with AI ", "OPTIONS": {"REPLY_SUGGESTION": "Reply Suggestion", "SUMMARIZE": "Summarize", "REPHRASE": "Improve Writing", "FIX_SPELLING_GRAMMAR": "Fix Spelling and Grammar", "SHORTEN": "<PERSON>en", "EXPAND": "Expand", "MAKE_FRIENDLY": "Change message tone to friendly", "MAKE_FORMAL": "Use formal tone", "SIMPLIFY": "Simplify"}, "ASSISTANCE_MODAL": {"DRAFT_TITLE": "Draft content", "GENERATED_TITLE": "Generated content", "AI_WRITING": "AI is writing", "BUTTONS": {"APPLY": "Use this suggestion", "CANCEL": "Batalkan"}}, "CTA_MODAL": {"TITLE": "Integrate with OpenAI", "DESC": "Bring advanced AI features to your dashboard with OpenAI's GPT models. To begin, enter the API key from your OpenAI account.", "KEY_PLACEHOLDER": "Enter your OpenAI API key", "BUTTONS": {"NEED_HELP": "Butuh bantuan?", "DISMISS": "<PERSON><PERSON><PERSON>", "FINISH": "Finish Setup"}, "DISMISS_MESSAGE": "You can setup OpenAI integration later Whenever you want.", "SUCCESS_MESSAGE": "OpenAI integration setup successfully"}, "TITLE": "Perbaiki dengan AI", "SUMMARY_TITLE": "<PERSON><PERSON><PERSON>", "REPLY_TITLE": "<PERSON><PERSON> b<PERSON><PERSON>", "SUBTITLE": "<PERSON><PERSON><PERSON> yang ditingkatkan akan dihasilkan menggunakan AI, berdasarkan draf saat ini.", "TONE": {"TITLE": "<PERSON><PERSON>", "OPTIONS": {"PROFESSIONAL": "Profesional", "FRIENDLY": "<PERSON><PERSON>"}}, "BUTTONS": {"GENERATE": "<PERSON><PERSON><PERSON>", "GENERATING": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "CANCEL": "Batalkan"}, "GENERATE_ERROR": "There was an error processing the content, please try again"}, "DELETE": {"BUTTON_TEXT": "Hapus", "API": {"SUCCESS_MESSAGE": "<PERSON>te<PERSON><PERSON> ber<PERSON> di<PERSON>"}}, "CONNECT": {"BUTTON_TEXT": "Sambungkan"}, "DASHBOARD_APPS": {"TITLE": "Aplikasi Dasbor", "HEADER_BTN_TXT": "Tambahkan aplikasi dasbor baru", "SIDEBAR_TXT": "<p><b>Aplikasi <PERSON>bor</b></p><p>Aplikasi Dasbor memungkinkan organisasi untuk menyematkan aplikasi di dalam dasbor AI Agent-OK untuk menyediakan konteks bagi agen dukungan pelanggan. Fitur ini memungkinkan Anda membuat aplikasi secara independen dan menyematkannya di dalam dasbor untuk menyediakan informasi pengguna, pesanan, atau riwayat pembayaran sebelumnya.</p><p>Ketika Anda menyematkan aplikasi Anda menggunakan dasbor di AI Agent-OK, aplikasi Anda akan mendapatkan konteks percakapan dan kontak sebagai acara window. Implementasikan penerima untuk acara pesan di halaman Anda untuk menerima konteks.</p><p>Untuk menambahkan aplikasi dasbor baru, klik tombol 'Tambahkan aplikasi dasbor baru'.</p>", "DESCRIPTION": "Aplikasi Dasbor memungkinkan organisasi untuk menyematkan aplikasi di dalam dasbor untuk menyediakan konteks bagi agen dukungan pelanggan. Fitur ini memungkinkan Anda membuat aplikasi secara independen dan menyematkannya untuk menyediakan informasi pengguna, pesanan, atau riwayat pembayaran sebelumnya.", "LEARN_MORE": "Learn more about Dashboard Apps", "LIST": {"404": "Belum ada aplikasi dasbor yang dikonfigurasi untuk akun ini", "LOADING": "Mengambil aplikasi dasbor...", "TABLE_HEADER": {"NAME": "<PERSON><PERSON>", "ENDPOINT": "<PERSON><PERSON><PERSON>"}, "EDIT_TOOLTIP": "<PERSON>", "DELETE_TOOLTIP": "<PERSON><PERSON>"}, "FORM": {"TITLE_LABEL": "<PERSON><PERSON>", "TITLE_PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON> nama aplikasi dasbor <PERSON>", "TITLE_ERROR": "<PERSON>a untuk aplikasi dasbor diperlukan", "URL_LABEL": "<PERSON><PERSON><PERSON>", "URL_PLACEHOLDER": "Masukkan URL endpoint tempat aplikasi Anda dihosting", "URL_ERROR": "URL valid diperlukan"}, "CREATE": {"HEADER": "Tambahkan aplikasi dasbor baru", "FORM_SUBMIT": "<PERSON><PERSON>", "FORM_CANCEL": "Batalkan", "API_SUCCESS": "Aplikasi das<PERSON> be<PERSON><PERSON><PERSON>", "API_ERROR": "<PERSON><PERSON> tidak dapat membuat aplikasi. Harap coba lagi nanti"}, "UPDATE": {"HEADER": "Edit aplikasi <PERSON>", "FORM_SUBMIT": "<PERSON><PERSON><PERSON>", "FORM_CANCEL": "Batalkan", "API_SUCCESS": "Aplikasi dasbor ber<PERSON>", "API_ERROR": "<PERSON><PERSON> tidak dapat memperbarui aplikasi. Harap coba lagi nanti"}, "DELETE": {"CONFIRM_YES": "Ya, hapus", "CONFIRM_NO": "Tidak, simpan", "TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MESSAGE": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus aplikasi - {appName}?", "API_SUCCESS": "Aplikasi das<PERSON> ber<PERSON><PERSON>", "API_ERROR": "<PERSON><PERSON> tidak dapat menghapus aplikasi. Harap coba lagi nanti"}}, "LINEAR": {"ADD_OR_LINK_BUTTON": "Create/Link Linear Issue", "LOADING": "Fetching linear issues...", "LOADING_ERROR": "There was an error fetching the linear issues, please try again", "CREATE": "Buat", "LINK": {"SEARCH": "Search issues", "SELECT": "Select issue", "TITLE": "Tautan", "EMPTY_LIST": "No linear issues found", "LOADING": "Loading", "ERROR": "There was an error fetching the linear issues, please try again", "LINK_SUCCESS": "Issue linked successfully", "LINK_ERROR": "There was an error linking the issue, please try again", "LINK_TITLE": "Conversation (#{conversationId}) with {name}"}, "ADD_OR_LINK": {"TITLE": "Create/link linear issue", "DESCRIPTION": "Create Linear issues from conversations, or link existing ones for seamless tracking.", "FORM": {"TITLE": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACEHOLDER": "Enter title", "REQUIRED_ERROR": "<PERSON><PERSON><PERSON> wa<PERSON><PERSON>"}, "DESCRIPTION": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Enter description"}, "TEAM": {"LABEL": "<PERSON>", "PLACEHOLDER": "<PERSON><PERSON><PERSON> tim", "SEARCH": "Search team", "REQUIRED_ERROR": "Team is required"}, "ASSIGNEE": {"LABEL": "Assignee", "PLACEHOLDER": "Select assignee", "SEARCH": "Search assignee"}, "PRIORITY": {"LABEL": "Prioritas", "PLACEHOLDER": "<PERSON><PERSON><PERSON> prioritas", "SEARCH": "Search priority"}, "LABEL": {"LABEL": "Label", "PLACEHOLDER": "Select label", "SEARCH": "Search label"}, "STATUS": {"LABEL": "Status", "PLACEHOLDER": "Select status", "SEARCH": "Search status"}, "PROJECT": {"LABEL": "Project", "PLACEHOLDER": "Select project", "SEARCH": "Search project"}}, "CREATE": "Buat", "CANCEL": "Batalkan", "CREATE_SUCCESS": "Issue created successfully", "CREATE_ERROR": "There was an error creating the issue, please try again", "LOADING_TEAM_ERROR": "There was an error fetching the teams, please try again", "LOADING_TEAM_ENTITIES_ERROR": "There was an error fetching the team entities, please try again"}, "ISSUE": {"STATUS": "Status", "PRIORITY": "Prioritas", "ASSIGNEE": "Assignee", "LABELS": "Label", "CREATED_AT": "Created at {createdAt}"}, "UNLINK": {"TITLE": "Unlink", "SUCCESS": "Issue unlinked successfully", "ERROR": "There was an error unlinking the issue, please try again"}, "DELETE": {"TITLE": "Are you sure you want to delete the integration?", "MESSAGE": "Are you sure you want to delete the integration?", "CONFIRM": "Ya, hapus", "CANCEL": "Batalkan"}}}, "CAPTAIN": {"NAME": "Captain", "HEADER_KNOW_MORE": "Know more", "COPILOT": {"SEND_MESSAGE": "<PERSON><PERSON>...", "LOADER": "Captain is thinking", "YOU": "<PERSON><PERSON>", "USE": "Use this", "RESET": "Reset", "SELECT_ASSISTANT": "Select Assistant"}, "PAYWALL": {"TITLE": "Upgrade to use Captain AI", "AVAILABLE_ON": "Captain is not available on the free plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "UPGRADE_NOW": "Upgrade now", "CANCEL_ANYTIME": "You can change or cancel your plan anytime"}, "ENTERPRISE_PAYWALL": {"AVAILABLE_ON": "Captain AI feature is only available in a paid plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "ASK_ADMIN": "Please reach out to your administrator for the upgrade."}, "BANNER": {"RESPONSES": "You've used over 80% of your response limit. To continue using Captain AI, please upgrade.", "DOCUMENTS": "Document limit reached. Upgrade to continue using Captain AI."}, "FORM": {"CANCEL": "Batalkan", "CREATE": "Buat", "EDIT": "<PERSON><PERSON><PERSON>"}, "ASSISTANTS": {"HEADER": "Assistants", "ADD_NEW": "Create a new assistant", "DELETE": {"TITLE": "Are you sure to delete the assistant?", "DESCRIPTION": "This action is permanent. Deleting this assistant will remove it from all connected inboxes and permanently erase all generated knowledge.", "CONFIRM": "Ya, hapus", "SUCCESS_MESSAGE": "The assistant has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the assistant, please try again."}, "FORM_DESCRIPTION": "Fill out the details below to name your assistant, describe its purpose, and specify the product it will support.", "CREATE": {"TITLE": "Create an assistant", "SUCCESS_MESSAGE": "The assistant has been successfully created", "ERROR_MESSAGE": "There was an error creating the assistant, please try again."}, "FORM": {"NAME": {"LABEL": "Assistant Name", "PLACEHOLDER": "Enter a name for the assistant", "ERROR": "Please provide a name for the assistant"}, "DESCRIPTION": {"LABEL": "Assistant Description", "PLACEHOLDER": "Describe how and where this assistant will be used", "ERROR": "A description is required"}, "PRODUCT_NAME": {"LABEL": "Product Name", "PLACEHOLDER": "Enter the name of the product this assistant is designed for", "ERROR": "The product name is required"}, "FEATURES": {"TITLE": "<PERSON><PERSON>", "ALLOW_CONVERSATION_FAQS": "Generate FAQs from resolved conversations", "ALLOW_MEMORIES": "Capture key details as memories from customer interactions."}}, "EDIT": {"TITLE": "Update the assistant", "SUCCESS_MESSAGE": "The assistant has been successfully updated", "ERROR_MESSAGE": "There was an error updating the assistant, please try again."}, "OPTIONS": {"EDIT_ASSISTANT": "Edit Assistant", "DELETE_ASSISTANT": "Delete Assistant", "VIEW_CONNECTED_INBOXES": "View connected inboxes"}, "EMPTY_STATE": {"TITLE": "No assistants available", "SUBTITLE": "Create an assistant to provide quick and accurate responses to your users. It can learn from your help articles and past conversations.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain Assistant", "NOTE": "Captain Assistant engages directly with customers, learns from your help docs and past conversations, and delivers instant, accurate responses. It handles the initial queries, providing quick resolutions before  transferring to an agent when needed."}}}, "DOCUMENTS": {"HEADER": "Documents", "ADD_NEW": "Create a new document", "RELATED_RESPONSES": {"TITLE": "Related FAQs", "DESCRIPTION": "These FAQs are generated directly from the document."}, "FORM_DESCRIPTION": "Enter the URL of the document to add it as a knowledge source and choose the assistant to associate it with.", "CREATE": {"TITLE": "Add a document", "SUCCESS_MESSAGE": "The document has been successfully created", "ERROR_MESSAGE": "There was an error creating the document, please try again."}, "FORM": {"URL": {"LABEL": "URL", "PLACEHOLDER": "Enter the URL of the document", "ERROR": "Please provide a valid URL for the document"}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select the assistant", "ERROR": "The assistant field is required"}}, "DELETE": {"TITLE": "Are you sure to delete the document?", "DESCRIPTION": "This action is permanent. Deleting this document will permanently erase all generated knowledge.", "CONFIRM": "Ya, hapus", "SUCCESS_MESSAGE": "The document has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the document, please try again."}, "OPTIONS": {"VIEW_RELATED_RESPONSES": "View Related Responses", "DELETE_DOCUMENT": "Delete Document"}, "EMPTY_STATE": {"TITLE": "No documents available", "SUBTITLE": "Documents are used by your assistant to generate FAQs. You can import documents to provide context for your assistant.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain <PERSON>ument", "NOTE": "A document in Captain serves as a knowledge resource for the assistant. By connecting your help center or guides, Captain can analyze the content and provide accurate responses for customer inquiries."}}}, "RESPONSES": {"HEADER": "FAQs", "ADD_NEW": "Create new FAQ", "DOCUMENTABLE": {"CONVERSATION": "Conversation #{id}"}, "SELECTED": "{count} selected", "BULK_APPROVE_BUTTON": "Approve", "BULK_DELETE_BUTTON": "Hapus", "BULK_APPROVE": {"SUCCESS_MESSAGE": "FAQs approved successfully", "ERROR_MESSAGE": "There was an error approving the FAQs, please try again."}, "BULK_DELETE": {"TITLE": "Delete FAQs?", "DESCRIPTION": "Are you sure you want to delete the selected FAQs? This action cannot be undone.", "CONFIRM": "Yes, delete all", "SUCCESS_MESSAGE": "FAQs deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQs, please try again."}, "DELETE": {"TITLE": "Are you sure to delete the FAQ?", "DESCRIPTION": "", "CONFIRM": "Ya, hapus", "SUCCESS_MESSAGE": "FAQ deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQ, please try again."}, "FILTER": {"ASSISTANT": "Assistant: {selected}", "STATUS": "Status: {selected}", "ALL_ASSISTANTS": "<PERSON><PERSON><PERSON>"}, "STATUS": {"TITLE": "Status", "PENDING": "<PERSON><PERSON><PERSON>", "APPROVED": "Approved", "ALL": "<PERSON><PERSON><PERSON>"}, "FORM_DESCRIPTION": "Add a question and its corresponding answer to the knowledge base and select the assistant it should be associated with.", "CREATE": {"TITLE": "Add an FAQ", "SUCCESS_MESSAGE": "The response has been added successfully.", "ERROR_MESSAGE": "An error occurred while adding the response. Please try again."}, "FORM": {"QUESTION": {"LABEL": "Question", "PLACEHOLDER": "Enter the question here", "ERROR": "Please provide a valid question."}, "ANSWER": {"LABEL": "Answer", "PLACEHOLDER": "Enter the answer here", "ERROR": "Please provide a valid answer."}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select an assistant", "ERROR": "Please select an assistant."}}, "EDIT": {"TITLE": "Update the FAQ", "SUCCESS_MESSAGE": "The FAQ has been successfully updated", "ERROR_MESSAGE": "There was an error updating the FAQ, please try again", "APPROVE_SUCCESS_MESSAGE": "The FAQ was marked as approved"}, "OPTIONS": {"APPROVE": "Mark as approved", "EDIT_RESPONSE": "Edit FAQ", "DELETE_RESPONSE": "Delete FAQ"}, "EMPTY_STATE": {"TITLE": "No FAQs Found", "SUBTITLE": "FAQs help your assistant provide quick and accurate answers to questions from your customers. They can be generated automatically from your content or can be added manually.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain FAQ", "NOTE": "Captain <PERSON><PERSON><PERSON> detects common customer questions—whether missing from your knowledge base or frequently asked—and generates relevant FAQs to improve support. You can review each suggestion and decide whether to approve or reject it."}}}, "INBOXES": {"HEADER": "Connected Inboxes", "ADD_NEW": "Connect a new inbox", "OPTIONS": {"DISCONNECT": "<PERSON><PERSON><PERSON> kone<PERSON>i"}, "DELETE": {"TITLE": "Are you sure to disconnect the inbox?", "DESCRIPTION": "", "CONFIRM": "Ya, hapus", "SUCCESS_MESSAGE": "The inbox was successfully disconnected.", "ERROR_MESSAGE": "There was an error disconnecting the inbox, please try again."}, "FORM_DESCRIPTION": "Choose an inbox to connect with the assistant.", "CREATE": {"TITLE": "Connect an Inbox", "SUCCESS_MESSAGE": "The inbox was successfully connected.", "ERROR_MESSAGE": "An error occurred while connecting the inbox. Please try again."}, "FORM": {"INBOX": {"LABEL": "Kotak masuk", "PLACEHOLDER": "Choose the inbox to deploy the assistant.", "ERROR": "An inbox selection is required."}}, "EMPTY_STATE": {"TITLE": "No Connected Inboxes", "SUBTITLE": "Connecting an inbox allows the assistant to handle initial questions from your customers before transferring them to you."}}}}