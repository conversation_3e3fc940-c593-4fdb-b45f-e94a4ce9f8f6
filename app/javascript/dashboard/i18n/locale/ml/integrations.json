{"INTEGRATION_SETTINGS": {"SHOPIFY": {"DELETE": {"TITLE": "Delete Shopify Integration", "MESSAGE": "Are you sure you want to delete the Shopify integration?"}, "STORE_URL": {"TITLE": "Connect Shopify Store", "LABEL": "Store URL", "PLACEHOLDER": "your-store.myshopify.com", "HELP": "Enter your Shopify store's myshopify.com URL", "CANCEL": "റദ്ദാക്കുക", "SUBMIT": "Connect Store"}, "ERROR": "There was an error connecting to Shopify. Please try again or contact support if the issue persists."}, "HEADER": "സംയോജനങ്ങൾ", "DESCRIPTION": "AI Agent-OK integrates with multiple tools and services to improve your team's efficiency. Explore the list below to configure your favorite apps.", "LEARN_MORE": "Learn more about integrations", "LOADING": "Fetching integrations", "CAPTAIN": {"DISABLED": "Captain is not enabled on your account.", "CLICK_HERE_TO_CONFIGURE": "Click here to configure", "LOADING_CONSOLE": "Loading Captain <PERSON><PERSON><PERSON>...", "FAILED_TO_LOAD_CONSOLE": "Failed to load Captain <PERSON><PERSON>. Please refresh and try again."}, "WEBHOOK": {"SUBSCRIBED_EVENTS": "Subscribed Events", "LEARN_MORE": "Learn more about webhooks", "FORM": {"CANCEL": "റദ്ദാക്കുക", "DESC": "നിങ്ങളുടെ ചാറ്റ് വൂട്ട് അക്കൗണ്ടിൽ എന്താണ് സംഭവിക്കുന്നതെന്നതിനെക്കുറിച്ചുള്ള തത്സമയ വിവരങ്ങൾ വെബ്‌ഹൂക്ക് ഇവന്റുകൾ നൽകുന്നു. ഒരു കോൾബാക്ക് കോൺഫിഗർ ചെയ്യുന്നതിന് സാധുവായ ഒരു യുആർഎൽ നൽകുക.", "SUBSCRIPTIONS": {"LABEL": "Events", "EVENTS": {"CONVERSATION_CREATED": "Conversation Created", "CONVERSATION_STATUS_CHANGED": "Conversation Status Changed", "CONVERSATION_UPDATED": "Conversation Updated", "MESSAGE_CREATED": "Message created", "MESSAGE_UPDATED": "Message updated", "WEBWIDGET_TRIGGERED": "Live chat widget opened by the user", "CONTACT_CREATED": "Contact created", "CONTACT_UPDATED": "Contact updated"}}, "END_POINT": {"LABEL": "വെബ്‌ഹുക്ക് യുആർഎൽ", "PLACEHOLDER": "Example: {webhookExampleURL}", "ERROR": "ദയവായി സാധുവായ ഒരു യുആർഎൽ നൽകുക"}, "EDIT_SUBMIT": "Update webhook", "ADD_SUBMIT": "വെബ്‌ഹുക്ക് സൃഷ്‌ടിക്കുക"}, "TITLE": "വെബ്‌ഹൂക്ക്", "CONFIGURE": "കോൺഫിഗർ", "HEADER": "വെബ്‌ഹൂക്ക് ക്രമീകരണങ്ങൾ", "HEADER_BTN_TXT": "പുതിയ വെബ്‌ഹൂക്ക് ഉണ്ടാക്കുക", "LOADING": "അറ്റാച്ചുചെയ്‌ത വെബ്‌ഹൂക്കുകൾ ലഭ്യമാക്കുന്നു", "SEARCH_404": "ഈ ചോദ്യവുമായി പൊരുത്തപ്പെടുന്ന ഇനങ്ങളൊന്നുമില്ല", "SIDEBAR_TXT": "<p><b>വെബ്‌ഹൂക്കുകൾ</b> </p> <p>എല്ലാ അക്കൗണ്ടിനും നിർവചിക്കാവുന്ന എച്ച്ടിടിപി കോൾബാക്കുകളാണ് വെബ്‌ഹൂക്കുകൾ. ചാറ്റ്‌വൂട്ടിലെ സന്ദേശ സൃഷ്‌ടിക്കൽ പോലുള്ള ഇവന്റുകളാണ് അവ പ്രവർത്തനക്ഷമമാക്കുന്നത്. ഈ അക്കൗണ്ടിനായി നിങ്ങൾക്ക് ഒന്നിൽ കൂടുതൽ വെബ്‌ഹൂക്കുകൾ സൃഷ്ടിക്കാൻ കഴിയും. <br /><br /> ഒരു <b>വെബ്‌ഹൂക്ക്</b> സൃഷ്‌ടിക്കുന്നതിന്, <b>പുതിയ വെബ്‌ഹൂക്ക് ഉണ്ടാക്കുക</b> ബട്ടണിൽ ക്ലിക്കുചെയ്യുക. ഇല്ലാതാക്കുക ബട്ടണിൽ ക്ലിക്കുചെയ്ത് നിങ്ങൾക്ക് നിലവിലുള്ള ഏതെങ്കിലും വെബ്‌ഹൂക്ക് നീക്കംചെയ്യാനും കഴിയും.</p>", "LIST": {"404": "ഈ അക്കൗണ്ടിനായി വെബ്‌ഹൂക്കുകളൊന്നും ക്രമീകരിച്ചിട്ടില്ല.", "TITLE": "വെബ്‌ഹൂക്കുകൾ നിയന്ത്രിക്കുക", "TABLE_HEADER": {"WEBHOOK_ENDPOINT": "വെബ്‌ഹൂക്ക് എൻഡ്‌പോയിന്റ്", "ACTIONS": "പ്രവർത്തനങ്ങൾ"}}, "EDIT": {"BUTTON_TEXT": "എഡിറ്റുചെയ്യുക", "TITLE": "വെബ്ഹുക്ക് എഡിറ്റ് ചെയ്യുക", "API": {"SUCCESS_MESSAGE": "Webhook configuration updated successfully", "ERROR_MESSAGE": "വൂട്ട് സെർവറിലേക്ക് കണക്റ്റുചെയ്യാനായില്ല, ദയവായി പിന്നീട് വീണ്ടും ശ്രമിക്കുക"}}, "ADD": {"CANCEL": "റദ്ദാക്കുക", "TITLE": "പുതിയ വെബ്‌ഹൂക്ക് ഉണ്ടാക്കുക", "API": {"SUCCESS_MESSAGE": "Webhook configuration added successfully", "ERROR_MESSAGE": "വൂട്ട് സെർവറിലേക്ക് കണക്റ്റുചെയ്യാനായില്ല, ദയവായി പിന്നീട് വീണ്ടും ശ്രമിക്കുക"}}, "DELETE": {"BUTTON_TEXT": "ഇല്ലാതാക്കുക", "API": {"SUCCESS_MESSAGE": "വെബ്‌ഹൂക്ക് വിജയകരമായി ഇല്ലാതാക്കി", "ERROR_MESSAGE": "വൂട്ട് സെർവറിലേക്ക് കണക്റ്റുചെയ്യാനായില്ല, ദയവായി പിന്നീട് വീണ്ടും ശ്രമിക്കുക"}, "CONFIRM": {"TITLE": "ഇല്ലാതാക്കൽ സ്ഥിരീകരിക്കുക", "MESSAGE": "Are you sure to delete the webhook? ({webhookURL})", "YES": "അതെ, ഇല്ലാതാക്കുക ", "NO": "ഇല്ല, സൂക്ഷിക്കുക"}}}, "SLACK": {"DELETE": "ഇല്ലാതാക്കുക", "DELETE_CONFIRMATION": {"TITLE": "Delete the integration", "MESSAGE": "Are you sure you want to delete the integration? Doing so will result in the loss of access to conversations on your Slack workspace."}, "HELP_TEXT": {"TITLE": "സ്ലാക്ക് ഇന്റഗ്രേഷൻ ഉപയോഗിക്കുന്നു", "BODY": "With this integration, all of your incoming conversations will be synced to the ***{selectedChannel<PERSON><PERSON>}*** channel in your Slack workspace. You can manage all your customer conversations right within the channel and never miss a message.\n\nHere are the main features of the integration:\n\n**Respond to conversations from within Slack:** To respond to a conversation in the ***{selectedChannelName}*** Slack channel, simply type out your message and send it as a thread. This will create a response back to the customer through AI Agent-OK. It's that simple!\n\n **Create private notes:** If you want to create private notes instead of replies, start your message with ***`note:`***. This ensures that your message is kept private and won't be visible to the customer.\n\n**Associate an agent profile:** If the person who replied on Slack has an agent profile in AI Agent-OK under the same email, the replies will be associated with that agent profile automatically. This means you can easily track who said what and when. On the other hand, when the replier doesn't have an associated agent profile, the replies will appear from the bot profile to the customer.", "SELECTED": "selected"}, "SELECT_CHANNEL": {"OPTION_LABEL": "Select a channel", "UPDATE": "അപ്‌ഡേറ്റ്", "BUTTON_TEXT": "Connect channel", "DESCRIPTION": "Your Slack workspace is now linked with AI Agent-OK. However, the integration is currently inactive. To activate the integration and connect a channel to AI Agent-OK, please click the button below.\n\n**Note:** If you are attempting to connect a private channel, add the AI Agent-OK app to the Slack channel before proceeding with this step.", "ATTENTION_REQUIRED": "Attention required", "EXPIRED": "Your Slack integration has expired. To continue receiving messages on Slack, please delete the integration and connect your workspace again."}, "UPDATE_ERROR": "There was an error updating the integration, please try again", "UPDATE_SUCCESS": "The channel is connected successfully", "FAILED_TO_FETCH_CHANNELS": "There was an error fetching the channels from Slack, please try again"}, "DYTE": {"CLICK_HERE_TO_JOIN": "Click here to join", "LEAVE_THE_ROOM": "Leave the room", "START_VIDEO_CALL_HELP_TEXT": "Start a new video call with the customer", "JOIN_ERROR": "There was an error joining the call, please try again", "CREATE_ERROR": "There was an error creating a meeting link, please try again"}, "OPEN_AI": {"AI_ASSIST": "AI Assist", "WITH_AI": " {option} with AI ", "OPTIONS": {"REPLY_SUGGESTION": "Reply Suggestion", "SUMMARIZE": "Summarize", "REPHRASE": "Improve Writing", "FIX_SPELLING_GRAMMAR": "Fix Spelling and Grammar", "SHORTEN": "<PERSON>en", "EXPAND": "Expand", "MAKE_FRIENDLY": "Change message tone to friendly", "MAKE_FORMAL": "Use formal tone", "SIMPLIFY": "Simplify"}, "ASSISTANCE_MODAL": {"DRAFT_TITLE": "Draft content", "GENERATED_TITLE": "Generated content", "AI_WRITING": "AI is writing", "BUTTONS": {"APPLY": "Use this suggestion", "CANCEL": "റദ്ദാക്കുക"}}, "CTA_MODAL": {"TITLE": "Integrate with OpenAI", "DESC": "Bring advanced AI features to your dashboard with OpenAI's GPT models. To begin, enter the API key from your OpenAI account.", "KEY_PLACEHOLDER": "Enter your OpenAI API key", "BUTTONS": {"NEED_HELP": "Need help?", "DISMISS": "<PERSON><PERSON><PERSON>", "FINISH": "Finish Setup"}, "DISMISS_MESSAGE": "You can setup OpenAI integration later Whenever you want.", "SUCCESS_MESSAGE": "OpenAI integration setup successfully"}, "TITLE": "Improve With AI", "SUMMARY_TITLE": "Summary with AI", "REPLY_TITLE": "Reply suggestion with AI", "SUBTITLE": "An improved reply will be generated using AI, based on your current draft.", "TONE": {"TITLE": "<PERSON><PERSON>", "OPTIONS": {"PROFESSIONAL": "Professional", "FRIENDLY": "Friendly"}}, "BUTTONS": {"GENERATE": "Generate", "GENERATING": "Generating...", "CANCEL": "റദ്ദാക്കുക"}, "GENERATE_ERROR": "There was an error processing the content, please try again"}, "DELETE": {"BUTTON_TEXT": "ഇല്ലാതാക്കുക", "API": {"SUCCESS_MESSAGE": "സംയോജനം വിജയകരമായി ഇല്ലാതാക്കി"}}, "CONNECT": {"BUTTON_TEXT": "ബന്ധിപ്പിക്കുക"}, "DASHBOARD_APPS": {"TITLE": "Dashboard Apps", "HEADER_BTN_TXT": "Add a new dashboard app", "SIDEBAR_TXT": "<p><b>Dashboard Apps</b></p><p>Dashboard Apps allow organizations to embed an application inside the AI Agent-OK dashboard to provide the context for customer support agents. This feature allows you to create an application independently and embed that inside the dashboard to provide user information, their orders, or their previous payment history.</p><p>When you embed your application using the dashboard in AI Agent-OK, your application will get the context of the conversation and contact as a window event. Implement a listener for the message event on your page to receive the context.</p><p>To add a new dashboard app, click on the button 'Add a new dashboard app'.</p>", "DESCRIPTION": "Dashboard Apps allow organizations to embed an application inside the dashboard to provide the context for customer support agents. This feature allows you to create an application independently and embed that to provide user information, their orders, or their previous payment history.", "LEARN_MORE": "Learn more about Dashboard Apps", "LIST": {"404": "There are no dashboard apps configured on this account yet", "LOADING": "Fetching dashboard apps...", "TABLE_HEADER": {"NAME": "പേര്", "ENDPOINT": "Endpoint"}, "EDIT_TOOLTIP": "Edit app", "DELETE_TOOLTIP": "Delete app"}, "FORM": {"TITLE_LABEL": "പേര്", "TITLE_PLACEHOLDER": "Enter a name for your dashboard app", "TITLE_ERROR": "A name for the dashboard app is required", "URL_LABEL": "Endpoint", "URL_PLACEHOLDER": "Enter the endpoint URL where your app is hosted", "URL_ERROR": "A valid URL is required"}, "CREATE": {"HEADER": "Add a new dashboard app", "FORM_SUBMIT": "സമർപ്പിക്കുക", "FORM_CANCEL": "റദ്ദാക്കുക", "API_SUCCESS": "Dashboard app configured successfully", "API_ERROR": "We couldn't create an app. Please try again later"}, "UPDATE": {"HEADER": "Edit dashboard app", "FORM_SUBMIT": "അപ്‌ഡേറ്റ്", "FORM_CANCEL": "റദ്ദാക്കുക", "API_SUCCESS": "Dashboard app updated successfully", "API_ERROR": "We couldn't update the app. Please try again later"}, "DELETE": {"CONFIRM_YES": "Yes, delete it", "CONFIRM_NO": "No, keep it", "TITLE": "Confirm deletion", "MESSAGE": "Are you sure to delete the app - {appName}?", "API_SUCCESS": "Dashboard app deleted successfully", "API_ERROR": "We couldn't delete the app. Please try again later"}}, "LINEAR": {"ADD_OR_LINK_BUTTON": "Create/Link Linear Issue", "LOADING": "Fetching linear issues...", "LOADING_ERROR": "There was an error fetching the linear issues, please try again", "CREATE": "സൃഷ്ടിക്കുക", "LINK": {"SEARCH": "Search issues", "SELECT": "Select issue", "TITLE": "Link", "EMPTY_LIST": "No linear issues found", "LOADING": "Loading", "ERROR": "There was an error fetching the linear issues, please try again", "LINK_SUCCESS": "Issue linked successfully", "LINK_ERROR": "There was an error linking the issue, please try again", "LINK_TITLE": "Conversation (#{conversationId}) with {name}"}, "ADD_OR_LINK": {"TITLE": "Create/link linear issue", "DESCRIPTION": "Create Linear issues from conversations, or link existing ones for seamless tracking.", "FORM": {"TITLE": {"LABEL": "തലക്കെട്ട്", "PLACEHOLDER": "Enter title", "REQUIRED_ERROR": "ശീർഷകം ആവശ്യമാണ്"}, "DESCRIPTION": {"LABEL": "വിവരണം", "PLACEHOLDER": "Enter description"}, "TEAM": {"LABEL": "Team", "PLACEHOLDER": "ടീം തിരഞ്ഞെടുക്കുക", "SEARCH": "Search team", "REQUIRED_ERROR": "Team is required"}, "ASSIGNEE": {"LABEL": "Assignee", "PLACEHOLDER": "Select assignee", "SEARCH": "Search assignee"}, "PRIORITY": {"LABEL": "Priority", "PLACEHOLDER": "Select priority", "SEARCH": "Search priority"}, "LABEL": {"LABEL": "Label", "PLACEHOLDER": "Select label", "SEARCH": "Search label"}, "STATUS": {"LABEL": "സ്റ്റാറ്റസ്", "PLACEHOLDER": "Select status", "SEARCH": "Search status"}, "PROJECT": {"LABEL": "Project", "PLACEHOLDER": "Select project", "SEARCH": "Search project"}}, "CREATE": "സൃഷ്ടിക്കുക", "CANCEL": "റദ്ദാക്കുക", "CREATE_SUCCESS": "Issue created successfully", "CREATE_ERROR": "There was an error creating the issue, please try again", "LOADING_TEAM_ERROR": "There was an error fetching the teams, please try again", "LOADING_TEAM_ENTITIES_ERROR": "There was an error fetching the team entities, please try again"}, "ISSUE": {"STATUS": "സ്റ്റാറ്റസ്", "PRIORITY": "Priority", "ASSIGNEE": "Assignee", "LABELS": "ലേബലുകൾ", "CREATED_AT": "Created at {createdAt}"}, "UNLINK": {"TITLE": "Unlink", "SUCCESS": "Issue unlinked successfully", "ERROR": "There was an error unlinking the issue, please try again"}, "DELETE": {"TITLE": "Are you sure you want to delete the integration?", "MESSAGE": "Are you sure you want to delete the integration?", "CONFIRM": "Yes, delete", "CANCEL": "റദ്ദാക്കുക"}}}, "CAPTAIN": {"NAME": "Captain", "HEADER_KNOW_MORE": "Know more", "COPILOT": {"SEND_MESSAGE": "സന്ദേശം അയയ്ക്കുക...", "LOADER": "Captain is thinking", "YOU": "You", "USE": "Use this", "RESET": "Reset", "SELECT_ASSISTANT": "Select Assistant"}, "PAYWALL": {"TITLE": "Upgrade to use Captain AI", "AVAILABLE_ON": "Captain is not available on the free plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "UPGRADE_NOW": "Upgrade now", "CANCEL_ANYTIME": "You can change or cancel your plan anytime"}, "ENTERPRISE_PAYWALL": {"AVAILABLE_ON": "Captain AI feature is only available in a paid plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "ASK_ADMIN": "Please reach out to your administrator for the upgrade."}, "BANNER": {"RESPONSES": "You've used over 80% of your response limit. To continue using Captain AI, please upgrade.", "DOCUMENTS": "Document limit reached. Upgrade to continue using Captain AI."}, "FORM": {"CANCEL": "റദ്ദാക്കുക", "CREATE": "സൃഷ്ടിക്കുക", "EDIT": "അപ്‌ഡേറ്റ്"}, "ASSISTANTS": {"HEADER": "Assistants", "ADD_NEW": "Create a new assistant", "DELETE": {"TITLE": "Are you sure to delete the assistant?", "DESCRIPTION": "This action is permanent. Deleting this assistant will remove it from all connected inboxes and permanently erase all generated knowledge.", "CONFIRM": "Yes, delete", "SUCCESS_MESSAGE": "The assistant has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the assistant, please try again."}, "FORM_DESCRIPTION": "Fill out the details below to name your assistant, describe its purpose, and specify the product it will support.", "CREATE": {"TITLE": "Create an assistant", "SUCCESS_MESSAGE": "The assistant has been successfully created", "ERROR_MESSAGE": "There was an error creating the assistant, please try again."}, "FORM": {"NAME": {"LABEL": "Assistant Name", "PLACEHOLDER": "Enter a name for the assistant", "ERROR": "Please provide a name for the assistant"}, "DESCRIPTION": {"LABEL": "Assistant Description", "PLACEHOLDER": "Describe how and where this assistant will be used", "ERROR": "A description is required"}, "PRODUCT_NAME": {"LABEL": "Product Name", "PLACEHOLDER": "Enter the name of the product this assistant is designed for", "ERROR": "The product name is required"}, "FEATURES": {"TITLE": "Features", "ALLOW_CONVERSATION_FAQS": "Generate FAQs from resolved conversations", "ALLOW_MEMORIES": "Capture key details as memories from customer interactions."}}, "EDIT": {"TITLE": "Update the assistant", "SUCCESS_MESSAGE": "The assistant has been successfully updated", "ERROR_MESSAGE": "There was an error updating the assistant, please try again."}, "OPTIONS": {"EDIT_ASSISTANT": "Edit Assistant", "DELETE_ASSISTANT": "Delete Assistant", "VIEW_CONNECTED_INBOXES": "View connected inboxes"}, "EMPTY_STATE": {"TITLE": "No assistants available", "SUBTITLE": "Create an assistant to provide quick and accurate responses to your users. It can learn from your help articles and past conversations.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain Assistant", "NOTE": "Captain Assistant engages directly with customers, learns from your help docs and past conversations, and delivers instant, accurate responses. It handles the initial queries, providing quick resolutions before  transferring to an agent when needed."}}}, "DOCUMENTS": {"HEADER": "Documents", "ADD_NEW": "Create a new document", "RELATED_RESPONSES": {"TITLE": "Related FAQs", "DESCRIPTION": "These FAQs are generated directly from the document."}, "FORM_DESCRIPTION": "Enter the URL of the document to add it as a knowledge source and choose the assistant to associate it with.", "CREATE": {"TITLE": "Add a document", "SUCCESS_MESSAGE": "The document has been successfully created", "ERROR_MESSAGE": "There was an error creating the document, please try again."}, "FORM": {"URL": {"LABEL": "URL", "PLACEHOLDER": "Enter the URL of the document", "ERROR": "Please provide a valid URL for the document"}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select the assistant", "ERROR": "The assistant field is required"}}, "DELETE": {"TITLE": "Are you sure to delete the document?", "DESCRIPTION": "This action is permanent. Deleting this document will permanently erase all generated knowledge.", "CONFIRM": "Yes, delete", "SUCCESS_MESSAGE": "The document has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the document, please try again."}, "OPTIONS": {"VIEW_RELATED_RESPONSES": "View Related Responses", "DELETE_DOCUMENT": "Delete Document"}, "EMPTY_STATE": {"TITLE": "No documents available", "SUBTITLE": "Documents are used by your assistant to generate FAQs. You can import documents to provide context for your assistant.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain <PERSON>ument", "NOTE": "A document in Captain serves as a knowledge resource for the assistant. By connecting your help center or guides, Captain can analyze the content and provide accurate responses for customer inquiries."}}}, "RESPONSES": {"HEADER": "FAQs", "ADD_NEW": "Create new FAQ", "DOCUMENTABLE": {"CONVERSATION": "Conversation #{id}"}, "SELECTED": "{count} selected", "BULK_APPROVE_BUTTON": "Approve", "BULK_DELETE_BUTTON": "ഇല്ലാതാക്കുക", "BULK_APPROVE": {"SUCCESS_MESSAGE": "FAQs approved successfully", "ERROR_MESSAGE": "There was an error approving the FAQs, please try again."}, "BULK_DELETE": {"TITLE": "Delete FAQs?", "DESCRIPTION": "Are you sure you want to delete the selected FAQs? This action cannot be undone.", "CONFIRM": "Yes, delete all", "SUCCESS_MESSAGE": "FAQs deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQs, please try again."}, "DELETE": {"TITLE": "Are you sure to delete the FAQ?", "DESCRIPTION": "", "CONFIRM": "Yes, delete", "SUCCESS_MESSAGE": "FAQ deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQ, please try again."}, "FILTER": {"ASSISTANT": "Assistant: {selected}", "STATUS": "Status: {selected}", "ALL_ASSISTANTS": "എല്ലാം"}, "STATUS": {"TITLE": "സ്റ്റാറ്റസ്", "PENDING": "കെട്ടിക്കിടക്കുന്നു", "APPROVED": "Approved", "ALL": "എല്ലാം"}, "FORM_DESCRIPTION": "Add a question and its corresponding answer to the knowledge base and select the assistant it should be associated with.", "CREATE": {"TITLE": "Add an FAQ", "SUCCESS_MESSAGE": "The response has been added successfully.", "ERROR_MESSAGE": "An error occurred while adding the response. Please try again."}, "FORM": {"QUESTION": {"LABEL": "Question", "PLACEHOLDER": "Enter the question here", "ERROR": "Please provide a valid question."}, "ANSWER": {"LABEL": "Answer", "PLACEHOLDER": "Enter the answer here", "ERROR": "Please provide a valid answer."}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select an assistant", "ERROR": "Please select an assistant."}}, "EDIT": {"TITLE": "Update the FAQ", "SUCCESS_MESSAGE": "The FAQ has been successfully updated", "ERROR_MESSAGE": "There was an error updating the FAQ, please try again", "APPROVE_SUCCESS_MESSAGE": "The FAQ was marked as approved"}, "OPTIONS": {"APPROVE": "Mark as approved", "EDIT_RESPONSE": "Edit FAQ", "DELETE_RESPONSE": "Delete FAQ"}, "EMPTY_STATE": {"TITLE": "No FAQs Found", "SUBTITLE": "FAQs help your assistant provide quick and accurate answers to questions from your customers. They can be generated automatically from your content or can be added manually.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain FAQ", "NOTE": "Captain <PERSON><PERSON><PERSON> detects common customer questions—whether missing from your knowledge base or frequently asked—and generates relevant FAQs to improve support. You can review each suggestion and decide whether to approve or reject it."}}}, "INBOXES": {"HEADER": "Connected Inboxes", "ADD_NEW": "Connect a new inbox", "OPTIONS": {"DISCONNECT": "Disconnect"}, "DELETE": {"TITLE": "Are you sure to disconnect the inbox?", "DESCRIPTION": "", "CONFIRM": "Yes, delete", "SUCCESS_MESSAGE": "The inbox was successfully disconnected.", "ERROR_MESSAGE": "There was an error disconnecting the inbox, please try again."}, "FORM_DESCRIPTION": "Choose an inbox to connect with the assistant.", "CREATE": {"TITLE": "Connect an Inbox", "SUCCESS_MESSAGE": "The inbox was successfully connected.", "ERROR_MESSAGE": "An error occurred while connecting the inbox. Please try again."}, "FORM": {"INBOX": {"LABEL": "ഇൻബോക്സ്", "PLACEHOLDER": "Choose the inbox to deploy the assistant.", "ERROR": "An inbox selection is required."}}, "EMPTY_STATE": {"TITLE": "No Connected Inboxes", "SUBTITLE": "Connecting an inbox allows the assistant to handle initial questions from your customers before transferring them to you."}}}}