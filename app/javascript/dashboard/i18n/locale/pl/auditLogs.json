{"AUDIT_LOGS": {"HEADER": "Dzienniki Audytu", "HEADER_BTN_TXT": "Do<PERSON>j Dzienn<PERSON> Audytu", "LOADING": "Pobieranie Dzienników Audytu", "DESCRIPTION": "Audit Logs maintain a record of activities in your account, allowing you to track and audit your account, team, or services.", "LEARN_MORE": "Learn more about audit logs", "SEARCH_404": "Brak elementów pasujących do tego zapytania", "SIDEBAR_TXT": "<p><b>Dzienniki Audytu</b></p><p>Dzienniki Audytu to ślady działań i zdarzeń w systemie AI Agent-OK.</p>", "LIST": {"404": "Brak dostępnych Dzienników Audytu na tym koncie.", "TITLE": "Zarządzaj Dziennikami Audytu", "DESC": "Dzienniki Audytu to ślady działań i zdarzeń w systemie AI Agent-OK.", "TABLE_HEADER": {"ACTIVITY": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TIME": "<PERSON><PERSON><PERSON><PERSON>", "IP_ADDRESS": "Adres IP"}}, "API": {"SUCCESS_MESSAGE": "Dzienniki Audytu pobrane pomyślnie", "ERROR_MESSAGE": "Nie można połączyć się z serwerem Woot, proszę spróbow<PERSON> później"}, "DEFAULT_USER": "System", "AUTOMATION_RULE": {"ADD": "{agentName} created a new automation rule (#{id})", "EDIT": "{agentName} updated an automation rule (#{id})", "DELETE": "{agent<PERSON>ame} deleted an automation rule (#{id})"}, "ACCOUNT_USER": {"ADD": "{agentName} z<PERSON><PERSON><PERSON><PERSON> {invitee} do konta jako {role}", "EDIT": {"SELF": "{agentName} zmie<PERSON>ł swoje {attributes} na {values}", "OTHER": "{agentName} z<PERSON><PERSON><PERSON> {attributes} użytkownika {user} na {values}", "DELETED": "{agent<PERSON><PERSON>} changed {attributes} of a deleted user to {values}"}}, "INBOX": {"ADD": "{agentName} created a new inbox (#{id})", "EDIT": "{agentName} updated an inbox (#{id})", "DELETE": "{agent<PERSON>ame} deleted an inbox (#{id})"}, "WEBHOOK": {"ADD": "{agent<PERSON>ame} created a new webhook (#{id})", "EDIT": "{agent<PERSON>ame} updated a webhook (#{id})", "DELETE": "{agent<PERSON><PERSON>} deleted a webhook (#{id})"}, "USER_ACTION": {"SIGN_IN": "{agentName} zalogował się", "SIGN_OUT": "{agentName} wyl<PERSON><PERSON><PERSON> się"}, "TEAM": {"ADD": "{<PERSON><PERSON><PERSON>} created a new team (#{id})", "EDIT": "{<PERSON><PERSON><PERSON>} updated a team (#{id})", "DELETE": "{<PERSON><PERSON><PERSON>} deleted a team (#{id})"}, "MACRO": {"ADD": "{agent<PERSON>ame} created a new macro (#{id})", "EDIT": "{agent<PERSON>ame} updated a macro (#{id})", "DELETE": "{agent<PERSON>ame} deleted a macro (#{id})"}, "INBOX_MEMBER": {"ADD": "{agent<PERSON>ame} added {user} to the inbox(#{inbox_id})", "REMOVE": "{<PERSON><PERSON><PERSON>} removed {user} from the inbox(#{inbox_id})"}, "TEAM_MEMBER": {"ADD": "{<PERSON><PERSON><PERSON>} added {user} to the team(#{team_id})", "REMOVE": "{<PERSON><PERSON><PERSON>} removed {user} from the team(#{team_id})"}, "ACCOUNT": {"EDIT": "{<PERSON><PERSON><PERSON>} updated the account configuration (#{id})"}}}