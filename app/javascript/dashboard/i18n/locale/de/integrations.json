{"INTEGRATION_SETTINGS": {"SHOPIFY": {"DELETE": {"TITLE": "Delete Shopify Integration", "MESSAGE": "Are you sure you want to delete the Shopify integration?"}, "STORE_URL": {"TITLE": "Connect Shopify Store", "LABEL": "Store URL", "PLACEHOLDER": "your-store.myshopify.com", "HELP": "Enter your Shopify store's myshopify.com URL", "CANCEL": "Stornieren", "SUBMIT": "Connect Store"}, "ERROR": "There was an error connecting to Shopify. Please try again or contact support if the issue persists."}, "HEADER": "Integrationen", "DESCRIPTION": "AI Agent-OK integriert sich mit mehreren Tools und Diensten, um die Effizienz Ihres Teams zu verbessern. Erkunden Sie die folgende Liste, um Ihre Lieblingsapps zu konfigurieren.", "LEARN_MORE": "Mehr über Integrationen erfahren", "LOADING": "Integrationen werden abgerufen", "CAPTAIN": {"DISABLED": "Captain ist auf <PERSON><PERSON><PERSON> nicht aktiviert.", "CLICK_HERE_TO_CONFIGURE": "<PERSON><PERSON> klicken, um zu konfigurieren", "LOADING_CONSOLE": "Captain-<PERSON><PERSON><PERSON> wird geladen...", "FAILED_TO_LOAD_CONSOLE": "Fehler beim Laden der Captain-Ko<PERSON>le. Bitte aktualisieren und erneut versuchen."}, "WEBHOOK": {"SUBSCRIBED_EVENTS": "Abonnierte Events", "LEARN_MORE": "Mehr über Webhooks erfahren", "FORM": {"CANCEL": "Stornieren", "DESC": "Webhook-Ereignisse bieten Ihnen Echtzeitinformationen darüber, was in Ihrem AI Agent-OK-Konto passiert. Bitte geben Sie eine gültige URL ein, um einen Rückruf zu konfigurieren.", "SUBSCRIPTIONS": {"LABEL": "Events", "EVENTS": {"CONVERSATION_CREATED": "Konversation erstellt", "CONVERSATION_STATUS_CHANGED": "Konversationsstatus geändert", "CONVERSATION_UPDATED": "Konversation aktualisiert", "MESSAGE_CREATED": "Nachricht erstellt", "MESSAGE_UPDATED": "Nachricht aktualisiert", "WEBWIDGET_TRIGGERED": "Vom Benutzer geöffnetes Live-Chat-Widget", "CONTACT_CREATED": "Kontakt erstellt", "CONTACT_UPDATED": "Kontakt aktualisiert"}}, "END_POINT": {"LABEL": "Webhook-URL", "PLACEHOLDER": "Beispiel: {webhookExampleURL}", "ERROR": "<PERSON>te geben Si<PERSON> eine gültige URL ein"}, "EDIT_SUBMIT": "Webhook aktualisieren", "ADD_SUBMIT": "Webhook erstellen"}, "TITLE": "Webhook", "CONFIGURE": "Konfigurieren", "HEADER": "Webhook-Einstellungen", "HEADER_BTN_TXT": "Neuen Webhook hinzufügen", "LOADING": "Angehängte Webhooks abrufen", "SEARCH_404": "<PERSON>s gibt keine Elemente, die dieser Abfrage entsprechen", "SIDEBAR_TXT": "<p> <b> Webhooks </b> </p> <p> Webhooks sind HTTP-R<PERSON><PERSON><PERSON>, die für jedes Konto definiert werden können. Sie werden durch Ereignisse wie die Erstellung von Nachrichten in AI Agent-OK ausgelöst. Sie können mehr als einen Webhook für dieses Konto erstellen. <br /> <br /> Um einen <b> Webhook </b> zu erstellen, klicken Sie auf die Schaltfläche <b> Neuen Webhook hinzufügen </b>. Sie können auch vorhandene Webhooks entfernen, indem Sie auf die Schaltfläche Löschen klicken. </p>", "LIST": {"404": "<PERSON><PERSON><PERSON> dieses Konto sind keine Webhooks konfiguriert.", "TITLE": "Webhooks verwalten", "TABLE_HEADER": {"WEBHOOK_ENDPOINT": "Webhook-Endpunkt", "ACTIONS": "Aktionen"}}, "EDIT": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON>", "TITLE": "Webhook bearbeiten", "API": {"SUCCESS_MESSAGE": "Webhook-Konfiguration erfolgreich aktualisiert", "ERROR_MESSAGE": "<PERSON>s konnte keine Verbindung zum Woot Server hergestellt werden. Bitte versuchen Sie es später erneut"}}, "ADD": {"CANCEL": "Stornieren", "TITLE": "Neuen Webhook hinzufügen", "API": {"SUCCESS_MESSAGE": "Webhook-Konfiguration erfolg<PERSON>ich hinzugefügt", "ERROR_MESSAGE": "<PERSON>s konnte keine Verbindung zum Woot Server hergestellt werden. Bitte versuchen Sie es später erneut"}}, "DELETE": {"BUTTON_TEXT": "Löschen", "API": {"SUCCESS_MESSAGE": "Webhook erfolgreich gelöscht", "ERROR_MESSAGE": "<PERSON>s konnte keine Verbindung zum Woot Server hergestellt werden. Bitte versuchen Sie es später erneut"}, "CONFIRM": {"TITLE": "Löschung bestätigen", "MESSAGE": "Möchten Sie den Webhook wirklich löschen? ({webhookURL})", "YES": "Ja, löschen ", "NO": "<PERSON><PERSON>, behalte es"}}}, "SLACK": {"DELETE": "Löschen", "DELETE_CONFIRMATION": {"TITLE": "Integration löschen", "MESSAGE": "<PERSON>d <PERSON> sic<PERSON>, dass Si<PERSON> die Integration löschen möchten? Dadurch verlieren Sie den Zugang zu Konversationen in Ihrem Slack-Arbeitsbereich."}, "HELP_TEXT": {"TITLE": "Slack-Integration verwenden", "BODY": "With this integration, all of your incoming conversations will be synced to the ***{selectedChannel<PERSON><PERSON>}*** channel in your Slack workspace. You can manage all your customer conversations right within the channel and never miss a message.\n\nHere are the main features of the integration:\n\n**Respond to conversations from within Slack:** To respond to a conversation in the ***{selectedChannelName}*** Slack channel, simply type out your message and send it as a thread. This will create a response back to the customer through AI Agent-OK. It's that simple!\n\n **Create private notes:** If you want to create private notes instead of replies, start your message with ***`note:`***. This ensures that your message is kept private and won't be visible to the customer.\n\n**Associate an agent profile:** If the person who replied on Slack has an agent profile in AI Agent-OK under the same email, the replies will be associated with that agent profile automatically. This means you can easily track who said what and when. On the other hand, when the replier doesn't have an associated agent profile, the replies will appear from the bot profile to the customer.", "SELECTED": "ausgewählt"}, "SELECT_CHANNEL": {"OPTION_LABEL": "Einen Kanal auswählen", "UPDATE": "Aktualisieren", "BUTTON_TEXT": "Kanal verbinden", "DESCRIPTION": "Ihr Slack-Arbeitsbereich ist jetzt mit AI Agent-OK verbunden. Die Integration ist jedoch derzeit inaktiv. Um die Integration zu aktivieren und einen Kanal mit AI Agent-OK zu verbinden, klicken Sie bitte auf den untenstehenden Button.\n\n**Hinweis:** <PERSON><PERSON>, einen privaten Kanal zu verbinden, fügen Sie die AI Agent-OK-App dem Slack-Kanal hinzu, bevor <PERSON>e diesen Schritt fortsetzen.", "ATTENTION_REQUIRED": "Achtung", "EXPIRED": "Ihre Slack-Integration ist abgelaufen. Um weiterhin Nachrichten auf Slack zu erhalten, löschen Sie bitte die Integration und verbinden Sie Ihren Arbeitsbereich erneut."}, "UPDATE_ERROR": "Beim Aktualisieren der Integration ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut", "UPDATE_SUCCESS": "Der Kanal wurde erfolgreich verbunden", "FAILED_TO_FETCH_CHANNELS": "Beim Abrufen der Kanäle von S<PERSON>ck ist ein Fehler aufgetreten. Bitte versuchen Si<PERSON> es erneut"}, "DYTE": {"CLICK_HERE_TO_JOIN": "<PERSON><PERSON><PERSON> hier, um beizutreten", "LEAVE_THE_ROOM": "<PERSON><PERSON> verlassen", "START_VIDEO_CALL_HELP_TEXT": "<PERSON>en neuen Videoanruf mit dem Kunden starten", "JOIN_ERROR": "<PERSON>im Verbinden des Anrufs ist ein Fehler aufgetreten, bitte versuche es erneut", "CREATE_ERROR": "<PERSON><PERSON> eines Meeting-Links ist ein Fehler aufgetreten, bitte versuchen Si<PERSON> es erneut"}, "OPEN_AI": {"AI_ASSIST": "AI-Assistent", "WITH_AI": " {option} mit KI ", "OPTIONS": {"REPLY_SUGGESTION": "Antwortvorschlag", "SUMMARIZE": "Zusammenfassen", "REPHRASE": "Schreibstil verbessern", "FIX_SPELLING_GRAMMAR": "Rechtschreibung und Grammatik korrigieren", "SHORTEN": "<PERSON><PERSON><PERSON><PERSON>", "EXPAND": "<PERSON><PERSON><PERSON><PERSON>", "MAKE_FRIENDLY": "Nachrichtenton in freundlich ändern", "MAKE_FORMAL": "<PERSON><PERSON><PERSON>n verwenden", "SIMPLIFY": "Vereinfachen"}, "ASSISTANCE_MODAL": {"DRAFT_TITLE": "Inhalt entwerfen", "GENERATED_TITLE": "Generierter Inhalt", "AI_WRITING": "Die KI schreibt", "BUTTONS": {"APPLY": "Diesen Vorschlag verwenden", "CANCEL": "Stornieren"}}, "CTA_MODAL": {"TITLE": "Mit OpenAI integrieren", "DESC": "Erweitern Sie Ihr Dashboard um fortschrittliche KI-Funktionen mit OpenAI's GPT-Modellen. Um loszulegen, geben Sie den API-Schlüssel aus Ihrem OpenAI-Konto ein.", "KEY_PLACEHOLDER": "Geben Sie Ihren OpenAI-API-Schlüssel ein", "BUTTONS": {"NEED_HELP": "Brauchen Sie Hilfe?", "DISMISS": "Verwerfen", "FINISH": "Einrichtung abschließen"}, "DISMISS_MESSAGE": "Sie können die OpenAI-Integration später jederzeit einrichten.", "SUCCESS_MESSAGE": "OpenAI-Integration erfolgreich eingerichtet"}, "TITLE": "Mit KI verbessern", "SUMMARY_TITLE": "Zusammenfassung mit KI", "REPLY_TITLE": "Vorschlag mit KI beantworten", "SUBTITLE": "Eine verbesserte Antwort wird mithilfe der KI generiert, basierend auf Ihrem aktuellen Entwurf.", "TONE": {"TITLE": "Ton", "OPTIONS": {"PROFESSIONAL": "Professionell", "FRIENDLY": "<PERSON><PERSON><PERSON><PERSON>"}}, "BUTTONS": {"GENERATE": "<PERSON><PERSON><PERSON>", "GENERATING": "Generieren...", "CANCEL": "Abbrechen"}, "GENERATE_ERROR": "Beim Verarbeiten des Inhalts ist ein Fehler aufgetreten, bitte versuchen Si<PERSON> es erneut"}, "DELETE": {"BUTTON_TEXT": "Löschen", "API": {"SUCCESS_MESSAGE": "Integration erfolgreich gelöscht"}}, "CONNECT": {"BUTTON_TEXT": "Verbinden"}, "DASHBOARD_APPS": {"TITLE": "Dashboard-Apps", "HEADER_BTN_TXT": "Eine neue Dashboard-<PERSON><PERSON> hi<PERSON>", "SIDEBAR_TXT": "<p><b>Dashboard Apps</b></p><p>Dashboard-Apps ermöglichen es Unternehmen, eine Anwendung in das AI Agent-OK-Dashboard einzubetten, um den Kontext für Kundendienstmitarbeiter bereitzustellen. Mit dieser Funktion können Sie eine Anwendung unabhängig erstellen und diese in das Dashboard einbetten, um Benutzerinformationen, ihre Bestellungen oder ihren bisherigen Zahlungsverlauf bereitzustellen.</p><p>Wenn Sie Ihre Anwendung über das Dashboard in AI Agent-OK einbetten, erhält Ihre Anwendung den Kontext der Unterhaltung und des Kontakts als Fensterereignis. Implementieren Sie einen Listener für das Nachrichtenereignis auf Ihrer Seite, um den Kontext zu erhalten. </p><p>Um eine neue Dashboard-App hinzuzufügen, klicken Sie auf die Schaltfläche „Neue Dashboard-App hinzufügen“.</p>", "DESCRIPTION": "Dashboard-Apps ermöglichen es Unternehmen, eine Anwendung in das Dashboard einzubetten, um den Kontext für Kundendienstmitarbeiter bereitzustellen. Mit dieser Funktion können Sie eine Anwendung unabhängig erstellen und diese einbetten, um Benutzerinformationen, ihre Bestellungen oder ihren bisherigen Zahlungsverlauf bereitzustellen.", "LEARN_MORE": "Mehr über Dashboard-<PERSON><PERSON> erfahren", "LIST": {"404": "<PERSON><PERSON><PERSON> dieses Konto sind noch keine Dashboard-<PERSON><PERSON> konfiguriert", "LOADING": "Dashboard-Apps werden abgerufen...", "TABLE_HEADER": {"NAME": "Name", "ENDPOINT": "Endpunkt"}, "EDIT_TOOLTIP": "App bearbeiten", "DELETE_TOOLTIP": "Anwendung löschen"}, "FORM": {"TITLE_LABEL": "Name", "TITLE_PLACEHOLDER": "<PERSON><PERSON><PERSON> Si<PERSON> einen Namen für Ihre Dashboard-App ein", "TITLE_ERROR": "Ein Name für die Dashboard-App ist erforderlich", "URL_LABEL": "Endpunkt", "URL_PLACEHOLDER": "Geben Sie die Endpunkt-URL ein, auf der Ihre App gehostet wird", "URL_ERROR": "Eine gültige URL ist erforderlich"}, "CREATE": {"HEADER": "Eine neue Dashboard-<PERSON><PERSON> hi<PERSON>", "FORM_SUBMIT": "Einreichen", "FORM_CANCEL": "Stornieren", "API_SUCCESS": "Dashboard-A<PERSON> erfolg<PERSON>ich konfiguriert", "API_ERROR": "Wir konnten keine A<PERSON> erstellen. Bitte versuchen Sie es später erneut"}, "UPDATE": {"HEADER": "Dashboard-App bearbeiten", "FORM_SUBMIT": "Aktualisieren", "FORM_CANCEL": "Stornieren", "API_SUCCESS": "Dashboard-App erfolgreich aktualisiert", "API_ERROR": "Wir konnten die App nicht aktualisieren. Bitte versuchen Sie es später erneut"}, "DELETE": {"CONFIRM_YES": "Ja, löschen", "CONFIRM_NO": "<PERSON><PERSON>, behalte es", "TITLE": "Löschen bestätigen", "MESSAGE": "<PERSON>öchten Sie die App {appName} wirklich löschen?", "API_SUCCESS": "Dashboard-A<PERSON> er<PERSON>lg<PERSON><PERSON>", "API_ERROR": "Wir konnten die App nicht löschen. Bitte versuchen Sie es später erneut"}}, "LINEAR": {"ADD_OR_LINK_BUTTON": "Lineares Problem erstellen/verknüpfen", "LOADING": "Lineare Probleme werden abgerufen...", "LOADING_ERROR": "Beim Abrufen der linearen Probleme ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut", "CREATE": "<PERSON><PERSON><PERSON><PERSON>", "LINK": {"SEARCH": "<PERSON>e suchen", "SELECT": "Problem auswählen", "TITLE": "Link", "EMPTY_LIST": "Keine linearen Probleme gefunden", "LOADING": "Wird geladen", "ERROR": "Beim Abrufen der linearen Probleme ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut", "LINK_SUCCESS": "Problem erfolgreich verknüpft", "LINK_ERROR": "<PERSON><PERSON>knüpfen des Problems ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut", "LINK_TITLE": "Unterhaltung (#{conversationId}) mit {name}"}, "ADD_OR_LINK": {"TITLE": "Lineares Problem erstellen/verknüpfen", "DESCRIPTION": "Erstellen Sie lineare Tickets aus Unterhaltungen oder verknüpfen Sie bestehende zur nahtlosen Verfolgung.", "FORM": {"TITLE": {"LABEL": "Titel", "PLACEHOLDER": "Titel e<PERSON>ben", "REQUIRED_ERROR": "Titel ist erforderlich"}, "DESCRIPTION": {"LABEL": "Beschreibung", "PLACEHOLDER": "Beschreibung eingeben"}, "TEAM": {"LABEL": "Team", "PLACEHOLDER": "Team auswählen", "SEARCH": "Team suchen", "REQUIRED_ERROR": "Team ist er<PERSON><PERSON>lich"}, "ASSIGNEE": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Zuständigen auswählen", "SEARCH": "Zuständigen suchen"}, "PRIORITY": {"LABEL": "Priorität", "PLACEHOLDER": "Priorität auswählen", "SEARCH": "Priorität suchen"}, "LABEL": {"LABEL": "Label", "PLACEHOLDER": "Label auswählen", "SEARCH": "Label suchen"}, "STATUS": {"LABEL": "Status", "PLACEHOLDER": "Status auswählen", "SEARCH": "Status suchen"}, "PROJECT": {"LABEL": "Projekt", "PLACEHOLDER": "Projekt auswählen", "SEARCH": "<PERSON>jekt suchen"}}, "CREATE": "<PERSON><PERSON><PERSON><PERSON>", "CANCEL": "Stornieren", "CREATE_SUCCESS": "Problem erfolgreich erstellt", "CREATE_ERROR": "<PERSON><PERSON>ellen des Problems ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut", "LOADING_TEAM_ERROR": "Beim Abrufen der Teams ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut", "LOADING_TEAM_ENTITIES_ERROR": "Beim Abrufen der Team-Entitäten ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut"}, "ISSUE": {"STATUS": "Status", "PRIORITY": "Priorität", "ASSIGNEE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LABELS": "Labels", "CREATED_AT": "Erstellt am {createdAt}"}, "UNLINK": {"TITLE": "Verknüpfung aufheben", "SUCCESS": "Problem erfolgreich getrennt", "ERROR": "<PERSON>im Aufheben der Verknüpfung des Problems ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut"}, "DELETE": {"TITLE": "Sind <PERSON> sicher, dass Si<PERSON> die Integration löschen möchten?", "MESSAGE": "Sind <PERSON> sicher, dass Si<PERSON> die Integration löschen möchten?", "CONFIRM": "Ja, löschen", "CANCEL": "Stornieren"}}}, "CAPTAIN": {"NAME": "<PERSON><PERSON>ä<PERSON>", "HEADER_KNOW_MORE": "Know more", "COPILOT": {"SEND_MESSAGE": "Nachricht senden...", "LOADER": "Captain denkt nach", "YOU": "<PERSON><PERSON>", "USE": "Verwenden", "RESET": "Z<PERSON>ücksetzen", "SELECT_ASSISTANT": "Assistent ausw<PERSON>hlen"}, "PAYWALL": {"TITLE": "Upgrade auf Captain AI", "AVAILABLE_ON": "Captain is not available on the free plan.", "UPGRADE_PROMPT": "<PERSON><PERSON><PERSON>, um Zugang zu unseren Assistenten, <PERSON><PERSON><PERSON> und mehr zu erhalten.", "UPGRADE_NOW": "Jetzt upgraden", "CANCEL_ANYTIME": "Sie können Ihr Paket jederzeit ändern oder kündigen"}, "ENTERPRISE_PAYWALL": {"AVAILABLE_ON": "Captain <PERSON> ist nur mit einem kostenpflichtigen Tarif verfü<PERSON>.", "UPGRADE_PROMPT": "<PERSON><PERSON><PERSON>, um Zugang zu unseren Assistenten, <PERSON><PERSON><PERSON> und mehr zu erhalten.", "ASK_ADMIN": "Bitte kontaktieren Sie Ihren Administrator für das Upgrade."}, "BANNER": {"RESPONSES": "You've used over 80% of your response limit. To continue using Captain AI, please upgrade.", "DOCUMENTS": "Dokumentenlimit erreicht. Upgraden um Cpatain AI weiter zu verwenden."}, "FORM": {"CANCEL": "Stornieren", "CREATE": "<PERSON><PERSON><PERSON><PERSON>", "EDIT": "Aktualisieren"}, "ASSISTANTS": {"HEADER": "Assistants", "ADD_NEW": "Create a new assistant", "DELETE": {"TITLE": "Are you sure to delete the assistant?", "DESCRIPTION": "This action is permanent. Deleting this assistant will remove it from all connected inboxes and permanently erase all generated knowledge.", "CONFIRM": "Ja, löschen", "SUCCESS_MESSAGE": "The assistant has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the assistant, please try again."}, "FORM_DESCRIPTION": "Fill out the details below to name your assistant, describe its purpose, and specify the product it will support.", "CREATE": {"TITLE": "Create an assistant", "SUCCESS_MESSAGE": "The assistant has been successfully created", "ERROR_MESSAGE": "There was an error creating the assistant, please try again."}, "FORM": {"NAME": {"LABEL": "Assistant Name", "PLACEHOLDER": "Enter a name for the assistant", "ERROR": "Please provide a name for the assistant"}, "DESCRIPTION": {"LABEL": "Assistant Description", "PLACEHOLDER": "Describe how and where this assistant will be used", "ERROR": "A description is required"}, "PRODUCT_NAME": {"LABEL": "Product Name", "PLACEHOLDER": "Enter the name of the product this assistant is designed for", "ERROR": "The product name is required"}, "FEATURES": {"TITLE": "Funktionen", "ALLOW_CONVERSATION_FAQS": "Generate FAQs from resolved conversations", "ALLOW_MEMORIES": "Capture key details as memories from customer interactions."}}, "EDIT": {"TITLE": "Update the assistant", "SUCCESS_MESSAGE": "The assistant has been successfully updated", "ERROR_MESSAGE": "There was an error updating the assistant, please try again."}, "OPTIONS": {"EDIT_ASSISTANT": "Edit Assistant", "DELETE_ASSISTANT": "Delete Assistant", "VIEW_CONNECTED_INBOXES": "View connected inboxes"}, "EMPTY_STATE": {"TITLE": "No assistants available", "SUBTITLE": "Create an assistant to provide quick and accurate responses to your users. It can learn from your help articles and past conversations.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain Assistant", "NOTE": "Captain Assistant engages directly with customers, learns from your help docs and past conversations, and delivers instant, accurate responses. It handles the initial queries, providing quick resolutions before  transferring to an agent when needed."}}}, "DOCUMENTS": {"HEADER": "Documents", "ADD_NEW": "Create a new document", "RELATED_RESPONSES": {"TITLE": "Related FAQs", "DESCRIPTION": "These FAQs are generated directly from the document."}, "FORM_DESCRIPTION": "Enter the URL of the document to add it as a knowledge source and choose the assistant to associate it with.", "CREATE": {"TITLE": "Add a document", "SUCCESS_MESSAGE": "The document has been successfully created", "ERROR_MESSAGE": "There was an error creating the document, please try again."}, "FORM": {"URL": {"LABEL": "URL", "PLACEHOLDER": "Enter the URL of the document", "ERROR": "Please provide a valid URL for the document"}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select the assistant", "ERROR": "The assistant field is required"}}, "DELETE": {"TITLE": "Are you sure to delete the document?", "DESCRIPTION": "This action is permanent. Deleting this document will permanently erase all generated knowledge.", "CONFIRM": "Ja, löschen", "SUCCESS_MESSAGE": "The document has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the document, please try again."}, "OPTIONS": {"VIEW_RELATED_RESPONSES": "View Related Responses", "DELETE_DOCUMENT": "Delete Document"}, "EMPTY_STATE": {"TITLE": "No documents available", "SUBTITLE": "Documents are used by your assistant to generate FAQs. You can import documents to provide context for your assistant.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain <PERSON>ument", "NOTE": "A document in Captain serves as a knowledge resource for the assistant. By connecting your help center or guides, Captain can analyze the content and provide accurate responses for customer inquiries."}}}, "RESPONSES": {"HEADER": "FAQs", "ADD_NEW": "Create new FAQ", "DOCUMENTABLE": {"CONVERSATION": "Conversation #{id}"}, "SELECTED": "{count} selected", "BULK_APPROVE_BUTTON": "Approve", "BULK_DELETE_BUTTON": "Löschen", "BULK_APPROVE": {"SUCCESS_MESSAGE": "FAQs approved successfully", "ERROR_MESSAGE": "There was an error approving the FAQs, please try again."}, "BULK_DELETE": {"TITLE": "Delete FAQs?", "DESCRIPTION": "Are you sure you want to delete the selected FAQs? This action cannot be undone.", "CONFIRM": "Yes, delete all", "SUCCESS_MESSAGE": "FAQs deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQs, please try again."}, "DELETE": {"TITLE": "Are you sure to delete the FAQ?", "DESCRIPTION": "", "CONFIRM": "Ja, löschen", "SUCCESS_MESSAGE": "FAQ deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQ, please try again."}, "FILTER": {"ASSISTANT": "Assistant: {selected}", "STATUS": "Status: {selected}", "ALL_ASSISTANTS": "Alle"}, "STATUS": {"TITLE": "Status", "PENDING": "<PERSON><PERSON><PERSON><PERSON>", "APPROVED": "Approved", "ALL": "Alle"}, "FORM_DESCRIPTION": "Add a question and its corresponding answer to the knowledge base and select the assistant it should be associated with.", "CREATE": {"TITLE": "Add an FAQ", "SUCCESS_MESSAGE": "The response has been added successfully.", "ERROR_MESSAGE": "An error occurred while adding the response. Please try again."}, "FORM": {"QUESTION": {"LABEL": "Question", "PLACEHOLDER": "Enter the question here", "ERROR": "Please provide a valid question."}, "ANSWER": {"LABEL": "Answer", "PLACEHOLDER": "Enter the answer here", "ERROR": "Please provide a valid answer."}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select an assistant", "ERROR": "Please select an assistant."}}, "EDIT": {"TITLE": "Update the FAQ", "SUCCESS_MESSAGE": "The FAQ has been successfully updated", "ERROR_MESSAGE": "There was an error updating the FAQ, please try again", "APPROVE_SUCCESS_MESSAGE": "The FAQ was marked as approved"}, "OPTIONS": {"APPROVE": "Mark as approved", "EDIT_RESPONSE": "Edit FAQ", "DELETE_RESPONSE": "Delete FAQ"}, "EMPTY_STATE": {"TITLE": "No FAQs Found", "SUBTITLE": "FAQs help your assistant provide quick and accurate answers to questions from your customers. They can be generated automatically from your content or can be added manually.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain FAQ", "NOTE": "Captain <PERSON><PERSON><PERSON> detects common customer questions—whether missing from your knowledge base or frequently asked—and generates relevant FAQs to improve support. You can review each suggestion and decide whether to approve or reject it."}}}, "INBOXES": {"HEADER": "Connected Inboxes", "ADD_NEW": "Connect a new inbox", "OPTIONS": {"DISCONNECT": "Verbindung trennen"}, "DELETE": {"TITLE": "Are you sure to disconnect the inbox?", "DESCRIPTION": "", "CONFIRM": "Ja, löschen", "SUCCESS_MESSAGE": "The inbox was successfully disconnected.", "ERROR_MESSAGE": "There was an error disconnecting the inbox, please try again."}, "FORM_DESCRIPTION": "Choose an inbox to connect with the assistant.", "CREATE": {"TITLE": "Connect an Inbox", "SUCCESS_MESSAGE": "The inbox was successfully connected.", "ERROR_MESSAGE": "An error occurred while connecting the inbox. Please try again."}, "FORM": {"INBOX": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Choose the inbox to deploy the assistant.", "ERROR": "An inbox selection is required."}}, "EMPTY_STATE": {"TITLE": "No Connected Inboxes", "SUBTITLE": "Connecting an inbox allows the assistant to handle initial questions from your customers before transferring them to you."}}}}