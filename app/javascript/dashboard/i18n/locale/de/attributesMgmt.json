{"ATTRIBUTES_MGMT": {"HEADER": "Benutzerdefinierte Attribute", "HEADER_BTN_TXT": "Benutzerdefiniertes Attribut hinzufügen", "LOADING": "Benutzerdefinierte Attribute abrufen", "DESCRIPTION": "A custom attribute tracks additional details about your contacts or conversations—such as the subscription plan or the date of their first purchase. You can add different types of custom attributes, such as text, lists, or numbers, to capture the specific information you need.", "LEARN_MORE": "Learn more about custom attributes", "ADD": {"TITLE": "Benutzerdefiniertes Attribut hinzufügen", "SUBMIT": "<PERSON><PERSON><PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "Abbrechen", "FORM": {"NAME": {"LABEL": "Anzeigename", "PLACEHOLDER": "Geben Sie den Anzeigenamen des benutzerdefinierten Attributs ein", "ERROR": "Name wird <PERSON><PERSON><PERSON><PERSON>"}, "DESC": {"LABEL": "Beschreibung", "PLACEHOLDER": "Geben Sie eine benutzerdefinierte Attributbeschreibung ein", "ERROR": "Beschreibung wird <PERSON>"}, "MODEL": {"LABEL": "<PERSON><PERSON>", "PLACEHOLDER": "Bitte auswählen", "ERROR": "<PERSON><PERSON> wird <PERSON>"}, "TYPE": {"LABEL": "<PERSON><PERSON>", "PLACEHOLDER": "Bitte Typ wählen", "ERROR": "<PERSON><PERSON> wird <PERSON>", "LIST": {"LABEL": "Listenwerte", "PLACEHOLDER": "Bitte Wert eingeben und Eingabetaste drücken", "ERROR": "Muss mindestens einen Wert haben"}}, "KEY": {"LABEL": "Schlüssel", "PLACEHOLDER": "Benutzerdefinierten Attributschlüssel eingeben", "ERROR": "Schlüssel ist erforderlich", "IN_VALID": "Ungült<PERSON> Schlüssel"}, "REGEX_PATTERN": {"LABEL": "Regex Muster", "PLACEHOLDER": "Bitte benutzerdefiniertes Attribut Regex Muster eingeben. (Optional)"}, "REGEX_CUE": {"LABEL": "Regex Hinweis", "PLACEHOLDER": "Bitte geben Si<PERSON> einen Hinweis zum Regex Muster ein. (Optional)"}, "ENABLE_REGEX": {"LABEL": "Regex Validierung aktivieren"}, "ENABLE_DEPENDENCY": {"LABEL": "Abhängigkeit aktivieren"}, "DEPENDENCY_CONFIG": {"TITLE": "Abhängigkeitskonfiguration"}, "DEPEND_ON_ATTRIBUTE": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Attribut auswählen"}, "DEPEND_ON_VALUE": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Erforderlichen Wert eingeben"}, "OPERATOR": {"LABEL": "Operator"}, "DEPEND_ON_REGEX": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Optional)", "PLACEHOLDER": "Regex-Muster für Textvalidierung eingeben", "HELP": "Falls angegeben, wird dieses Regex anstelle der exakten Wertübereinstimmung für Textattribute verwendet"}, "OPERATORS": {"EQUAL_TO": "<PERSON><PERSON><PERSON>", "NOT_EQUAL_TO": "<PERSON><PERSON> gleich", "CONTAINS": "<PERSON><PERSON><PERSON><PERSON>", "NOT_CONTAINS": "<PERSON><PERSON><PERSON><PERSON> nicht", "STARTS_WITH": "Beginnt mit", "ENDS_WITH": "<PERSON><PERSON> mit", "REGEX_MATCH": "Regex-Übereinstimmung", "CONTAINS_ANY": "Enthält beliebige", "NOT_CONTAINS_ANY": "<PERSON><PERSON><PERSON><PERSON> keine", "GREATER_THAN": "<PERSON><PERSON><PERSON><PERSON><PERSON> als", "LESS_THAN": "<PERSON><PERSON>s", "GREATER_THAN_OR_EQUAL": "<PERSON><PERSON><PERSON><PERSON><PERSON> oder gleich", "LESS_THAN_OR_EQUAL": "<PERSON>er oder gleich"}, "LOGICAL_OPERATORS": {"AND": "UND", "OR": "ODER"}, "CHECKBOX_OPTIONS": {"NO": "<PERSON><PERSON>", "YES": "<PERSON>a"}, "ADD_CONDITION": "Bedingung hinzufügen", "DEPENDENCY_VALIDATION_ERROR": "Bitte vervollständigen Sie alle Abhängigkeitsbedingungskonfigurationen"}, "API": {"SUCCESS_MESSAGE": "Benutzerdefiniertes Attribut erfolgreich hinzugefügt!", "ERROR_MESSAGE": "Konnte kein benutzerdefiniertes Attribut erstellen, bitte versuchen Sie es später erneut."}}, "DELETE": {"BUTTON_TEXT": "Löschen", "API": {"SUCCESS_MESSAGE": "Benutzerdefiniertes Attribut erfolgreich gelöscht.", "ERROR_MESSAGE": "Das benutzerdefinierte Attribut konnte nicht gelöscht werden. Versuchen Sie es noch einmal."}, "CONFIRM": {"TITLE": "<PERSON>d <PERSON> sic<PERSON>, dass <PERSON> {attributeName} löschen möchten", "PLACE_HOLDER": "<PERSON>te geben Sie {attributeName} zur Bestätigung ein", "MESSAGE": "Beim L<PERSON> wird das benutzerdefinierte Attribut entfernt", "YES": "Löschen ", "NO": "Abbrechen"}}, "EDIT": {"TITLE": "Benutzerdefiniertes Attribut bearbeiten", "UPDATE_BUTTON_TEXT": "Aktualisieren", "TYPE": {"LIST": {"LABEL": "Listenwerte", "PLACEHOLDER": "Bitte Werte eingeben und Enter-Taste drücken"}}, "API": {"SUCCESS_MESSAGE": "Benutzerdefiniertes Attribut erfolgreich aktualisiert", "ERROR_MESSAGE": "Beim Aktualisieren des benutzerdefinierten Attributs ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut"}}, "TABS": {"HEADER": "Benutzerdefinierte Attribute", "CONVERSATION": "Konversation", "CONTACT": "Kontakt"}, "LIST": {"TABLE_HEADER": {"NAME": "Name", "DESCRIPTION": "Beschreibung", "TYPE": "<PERSON><PERSON>", "KEY": "Schlüssel"}, "BUTTONS": {"EDIT": "<PERSON><PERSON><PERSON>", "DELETE": "Löschen"}, "EMPTY_RESULT": {"404": "<PERSON>s wurden keine benutzerdefinierten Attribute erstellt", "NOT_FOUND": "Es sind keine benutzerdefinierten Attribute konfiguriert"}, "REGEX_PATTERN": {"LABEL": "Regex Muster", "PLACEHOLDER": "Bitte benutzerdefiniertes Attribut Regex Muster eingeben. (Optional)"}, "REGEX_CUE": {"LABEL": "Regex Hinweis", "PLACEHOLDER": "Bitte geben Si<PERSON> einen Hinweis zum Regex Muster ein. (Optional)"}, "ENABLE_REGEX": {"LABEL": "Regex Validierung aktivieren"}}}}