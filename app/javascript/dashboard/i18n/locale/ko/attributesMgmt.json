{"ATTRIBUTES_MGMT": {"HEADER": "사용자 지정 특성", "HEADER_BTN_TXT": "사용자 지정 속성 추가하기", "LOADING": "사용자 지정 속성들 가져오기", "DESCRIPTION": "사용자 지정 속성은 연락처나 대화에 대한 추가 세부 정보(예: 구독 플랜이나 첫 구매 날짜)를 추적합니다. 텍스트, 목록, 숫자 등 다양한 유형의 사용자 지정 속성을 추가하여 필요한 특정 정보를 캡처할 수 있습니다.", "LEARN_MORE": "사용자 지정 속성에 대해 자세히 알아보기", "ADD": {"TITLE": "사용자 지정 속성 추가하기", "SUBMIT": "만들기", "CANCEL_BUTTON_TEXT": "취소", "FORM": {"NAME": {"LABEL": "표시명", "PLACEHOLDER": "사용자 지정 속성 표기명 작성하기", "ERROR": "이름이 필요합니다"}, "DESC": {"LABEL": "내용", "PLACEHOLDER": "사용자 지정 속성 설명 작성하기", "ERROR": "설명이 필요합니다"}, "MODEL": {"LABEL": "적용 대상", "PLACEHOLDER": "하나를 선택하세요", "ERROR": "적용 대상이 필요합니다"}, "TYPE": {"LABEL": "유형", "PLACEHOLDER": "유형을 선택하세요", "ERROR": "유형이 필요합니다", "LIST": {"LABEL": "목록 값", "PLACEHOLDER": "값을 입력하고 엔터 키를 누르세요", "ERROR": "최소한 하나의 값이 필요합니다"}}, "KEY": {"LABEL": "키", "PLACEHOLDER": "사용자 지정 속성 키 입력", "ERROR": "키가 필요합니다", "IN_VALID": "유효하지 않은 키"}, "REGEX_PATTERN": {"LABEL": "정규식 패턴", "PLACEHOLDER": "사용자 지정 속성 정규식 패턴을 입력하세요. (선택사항)"}, "REGEX_CUE": {"LABEL": "정규식 힌트", "PLACEHOLDER": "정규식 패턴 힌트를 입력하세요. (선택사항)"}, "ENABLE_REGEX": {"LABEL": "정규식 유효성 검사 활성화"}}, "API": {"SUCCESS_MESSAGE": "사용자 지정 속성이 성공적으로 추가되었습니다!", "ERROR_MESSAGE": "사용자 지정 속성을 생성할 수 없습니다. 나중에 다시 시도해주세요."}}, "DELETE": {"BUTTON_TEXT": "삭제", "API": {"SUCCESS_MESSAGE": "사용자 지정 속성이 성공적으로 삭제되었습니다.", "ERROR_MESSAGE": "사용자 지정 속성을 삭제할 수 없습니다. 다시 시도해주세요."}, "CONFIRM": {"TITLE": "{attributeName}팀을 삭제하시겠습니까?", "PLACE_HOLDER": "확인을 위해 {attributeName}을(를) 입력하세요", "MESSAGE": "삭제하면 사용자 지정 속성이 제거됩니다", "YES": "삭제 ", "NO": "취소"}}, "EDIT": {"TITLE": "사용자 지정 속성 수정", "UPDATE_BUTTON_TEXT": "업데이트", "TYPE": {"LIST": {"LABEL": "목록 값", "PLACEHOLDER": "값을 입력하고 엔터 키를 누르세요"}}, "API": {"SUCCESS_MESSAGE": "사용자 지정 속성이 성공적으로 업데이트되었습니다", "ERROR_MESSAGE": "사용자 지정 속성 업데이트 중 오류가 발생했습니다. 다시 시도해주세요"}}, "TABS": {"HEADER": "사용자 지정 특성", "CONVERSATION": "대화", "CONTACT": "연락처"}, "LIST": {"TABLE_HEADER": {"NAME": "이름", "DESCRIPTION": "내용", "TYPE": "유형", "KEY": "키"}, "BUTTONS": {"EDIT": "수정", "DELETE": "삭제"}, "EMPTY_RESULT": {"404": "생성된 사용자 지정 속성이 없습니다", "NOT_FOUND": "구성된 사용자 지정 속성이 없습니다"}, "REGEX_PATTERN": {"LABEL": "정규식 패턴", "PLACEHOLDER": "사용자 지정 속성 정규식 패턴을 입력하세요. (선택사항)"}, "REGEX_CUE": {"LABEL": "정규식 힌트", "PLACEHOLDER": "정규식 패턴 힌트를 입력하세요. (선택사항)"}, "ENABLE_REGEX": {"LABEL": "정규식 유효성 검사 활성화"}}, "ENABLE_DEPENDENCY": {"LABEL": "종속성 활성화"}, "DEPENDENCY_CONFIG": {"TITLE": "종속성 구성"}, "DEPEND_ON_ATTRIBUTE": {"LABEL": "종속 속성", "PLACEHOLDER": "속성을 선택하세요"}, "DEPEND_ON_VALUE": {"LABEL": "종속 값", "PLACEHOLDER": "필요한 값을 입력하세요"}, "OPERATOR": {"LABEL": "연산자"}, "DEPEND_ON_REGEX": {"LABEL": "정규식 패턴에 종속 (선택사항)", "PLACEHOLDER": "텍스트 검증을 위한 정규식 패턴을 입력하세요", "HELP": "지정된 경우, 이 정규식이 텍스트 속성의 정확한 값 일치 대신 사용됩니다"}, "OPERATORS": {"EQUAL_TO": "같음", "NOT_EQUAL_TO": "같지 않음", "CONTAINS": "포함", "NOT_CONTAINS": "포함하지 않음", "STARTS_WITH": "시작함", "ENDS_WITH": "끝남", "REGEX_MATCH": "정규식 일치", "CONTAINS_ANY": "임의 포함", "NOT_CONTAINS_ANY": "아무것도 포함하지 않음", "GREATER_THAN": "보다 큼", "LESS_THAN": "보다 작음", "GREATER_THAN_OR_EQUAL": "크거나 같음", "LESS_THAN_OR_EQUAL": "작거나 같음"}, "LOGICAL_OPERATORS": {"AND": "그리고", "OR": "또는"}, "CHECKBOX_OPTIONS": {"NO": "아니오", "YES": "예"}, "ADD_CONDITION": "조건 추가", "DEPENDENCY_VALIDATION_ERROR": "모든 종속성 조건 구성을 완료하세요"}}