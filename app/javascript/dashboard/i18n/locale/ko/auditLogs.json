{"AUDIT_LOGS": {"HEADER": "감사 로그", "HEADER_BTN_TXT": "감사 로그 추가", "LOADING": "감사 로그 불러오는 중", "DESCRIPTION": "감사 로그는 계정의 활동 기록을 유지하여 계정, 팀 또는 서비스를 추적하고 감사할 수 있게 해줍니다.", "LEARN_MORE": "감사 로그에 대해 자세히 알아보기", "SEARCH_404": "이 쿼리와 일치하는 항목이 없음", "SIDEBAR_TXT": "<p><b>감사 로그</b> </p><p> 감사 로그는 AI비서 오팀장 시스템의 이벤트와 작업에 대한 기록입니다. </p>", "LIST": {"404": "이 계정에서 사용 가능한 감사 로그가 없습니다.", "TITLE": "감사 로그 관리", "DESC": "감사 로그는 AI비서 오팀장 시스템의 이벤트와 작업에 대한 기록입니다.", "TABLE_HEADER": {"ACTIVITY": "사용자", "TIME": "작업", "IP_ADDRESS": "IP 주소"}}, "API": {"SUCCESS_MESSAGE": "감사 로그를 성공적으로 불러왔습니다", "ERROR_MESSAGE": "Woot 서버에 연결할 수 없음. 나중에 다시 시도하십시오."}, "DEFAULT_USER": "시스템", "AUTOMATION_RULE": {"ADD": "{agentName}님이 새로운 자동화 규칙을 생성했습니다 (#{id})", "EDIT": "{agentName}님이 자동화 규칙을 업데이트했습니다 (#{id})", "DELETE": "{agentName}님이 자동화 규칙을 삭제했습니다 (#{id})"}, "ACCOUNT_USER": {"ADD": "{agent<PERSON>ame}님이 {invitee}님을 {role}로 계정에 초대했습니다", "EDIT": {"SELF": "{agentName}님이 자신의 {attributes}을(를) {values}(으)로 변경했습니다", "OTHER": "{agentName}님이 {user}님의 {attributes}을(를) {values}(으)로 변경했습니다", "DELETED": "{agentName}님이 삭제된 사용자의 {attributes}을(를) {values}(으)로 변경했습니다"}}, "INBOX": {"ADD": "{agentName}님이 새로운 수신함을 생성했습니다 (#{id})", "EDIT": "{agentName}님이 수신함을 업데이트했습니다 (#{id})", "DELETE": "{agentName}님이 수신함을 삭제했습니다 (#{id})"}, "WEBHOOK": {"ADD": "{agentName}님이 새로운 웹훅을 생성했습니다 (#{id})", "EDIT": "{agentName}님이 웹훅을 업데이트했습니다 (#{id})", "DELETE": "{agentName}님이 웹훅을 삭제했습니다 (#{id})"}, "USER_ACTION": {"SIGN_IN": "{agent<PERSON>ame}님이 로그인했습니다", "SIGN_OUT": "{agent<PERSON>ame}님이 로그아웃했습니다"}, "TEAM": {"ADD": "{agentName}님이 새로운 팀을 생성했습니다 (#{id})", "EDIT": "{agentName}님이 팀을 업데이트했습니다 (#{id})", "DELETE": "{agentName}님이 팀을 삭제했습니다 (#{id})"}, "MACRO": {"ADD": "{agentName}님이 새로운 매크로를 생성했습니다 (#{id})", "EDIT": "{agentName}님이 매크로를 업데이트했습니다 (#{id})", "DELETE": "{agentName}님이 매크로를 삭제했습니다 (#{id})"}, "INBOX_MEMBER": {"ADD": "{agentName}님이 {user}님을 수신함(#{inbox_id})에 추가했습니다", "REMOVE": "{agentName}님이 {user}님을 수신함(#{inbox_id})에서 제거했습니다"}, "TEAM_MEMBER": {"ADD": "{agentName}님이 {user}님을 팀(#{team_id})에 추가했습니다", "REMOVE": "{agentName}님이 {user}님을 팀(#{team_id})에서 제거했습니다"}, "ACCOUNT": {"EDIT": "{agentName}님이 계정 설정을 업데이트했습니다 (#{id})"}}}