{"INTEGRATION_SETTINGS": {"SHOPIFY": {"DELETE": {"TITLE": "Shopify 통합 삭제", "MESSAGE": "Shopify 통합을 삭제하시겠습니까?"}, "STORE_URL": {"TITLE": "Shopify 스토어 연결", "LABEL": "스토어 URL", "PLACEHOLDER": "your-store.myshopify.com", "HELP": "Shopify 스토어의 myshopify.com URL을 입력하세요", "CANCEL": "취소", "SUBMIT": "스토어 연결"}, "ERROR": "Shopify에 연결하는 중 오류가 발생했습니다. 다시 시도하거나 문제가 지속되면 지원팀에 문의하세요."}, "HEADER": "통합", "DESCRIPTION": "AI비서 오팀장는 팀의 효율성을 향상시키기 위해 다양한 도구와 서비스를 통합합니다. 아래 목록을 살펴보고 선호하는 앱을 구성하세요.", "LEARN_MORE": "통합에 대해 자세히 알아보기", "LOADING": "통합 기능을 불러오는 중", "CAPTAIN": {"DISABLED": "Captain이 계정에서 활성화되지 않았습니다.", "CLICK_HERE_TO_CONFIGURE": "구성하려면 여기를 클릭하세요", "LOADING_CONSOLE": "Captain 콘솔을 불러오는 중...", "FAILED_TO_LOAD_CONSOLE": "Captain 콘솔을 불러오지 못했습니다. 새로 고침 후 다시 시도하세요."}, "WEBHOOK": {"SUBSCRIBED_EVENTS": "구독한 이벤트", "LEARN_MORE": "웹훅에 대해 자세히 알아보기", "FORM": {"CANCEL": "취소", "DESC": "웹훅 이벤트는 AI비서 오팀장 계정에서 일어나는 일에 대한 실시간 정보를 제공합니다. 콜백을 구성하려면 유효한 URL을 입력하십시오.", "SUBSCRIPTIONS": {"LABEL": "이벤트", "EVENTS": {"CONVERSATION_CREATED": "대화 생성됨", "CONVERSATION_STATUS_CHANGED": "대화 상태 변경됨", "CONVERSATION_UPDATED": "대화 업데이트됨", "MESSAGE_CREATED": "메시지 생성됨", "MESSAGE_UPDATED": "메시지 업데이트됨", "WEBWIDGET_TRIGGERED": "사용자가 실시간 채팅 위젯을 열었습니다", "CONTACT_CREATED": "연락처 생성됨", "CONTACT_UPDATED": "연락처 업데이트됨"}}, "END_POINT": {"LABEL": "웹훅 URL", "PLACEHOLDER": "예시: {webhookExampleURL}", "ERROR": "올바른 URL을 입력하십시오."}, "EDIT_SUBMIT": "웹훅 업데이트", "ADD_SUBMIT": "웹훅 만들기"}, "TITLE": "웹훅", "CONFIGURE": "구성", "HEADER": "웹훅 설정", "HEADER_BTN_TXT": "새 웹훅 추가", "LOADING": "첨부된 웹훅을 가져오는 중", "SEARCH_404": "이 쿼리와 일치하는 항목이 없음", "SIDEBAR_TXT": "<p><b>라벨</b></p><p>라벨은 대화를 분류하고 우선순위를 정하는 데 도움이 된다. 사이드패널에서 대화에 라벨을 할당할 수 있다.</p><p>라벨은 계정에 연결되며 조직에서 사용자 정의 워크플로우를 만드는 데 사용할 수 있다. 라벨에 사용자 정의 색상을 지정할 수 있으며, 라벨을 쉽게 식별할 수 있다. 사이드바에 라벨을 표시하여 대화를 쉽게 필터링할 수 있다.</p>", "LIST": {"404": "이 계정에 구성된 웹훅이 없음.", "TITLE": "웹훅 관리", "TABLE_HEADER": {"WEBHOOK_ENDPOINT": "웹훅 엔드포인트", "ACTIONS": "액션"}}, "EDIT": {"BUTTON_TEXT": "수정", "TITLE": "웹훅 수정", "API": {"SUCCESS_MESSAGE": "웹훅 구성이 성공적으로 업데이트되었습니다", "ERROR_MESSAGE": "Woot 서버에 연결할 수 없음. 나중에 다시 시도하십시오."}}, "ADD": {"CANCEL": "취소", "TITLE": "새 웹훅 추가", "API": {"SUCCESS_MESSAGE": "웹훅 구성이 성공적으로 추가되었습니다", "ERROR_MESSAGE": "Woot 서버에 연결할 수 없음. 나중에 다시 시도하십시오."}}, "DELETE": {"BUTTON_TEXT": "삭제", "API": {"SUCCESS_MESSAGE": "웹훅이 성공적으로 삭제됨", "ERROR_MESSAGE": "Woot 서버에 연결할 수 없음. 나중에 다시 시도하십시오."}, "CONFIRM": {"TITLE": "삭제 확인", "MESSAGE": "웹훅을 삭제하시겠습니까? ({webhookURL})", "YES": "예, 삭제합니다. ", "NO": "아니요, 유지합니다."}}}, "SLACK": {"DELETE": "삭제", "DELETE_CONFIRMATION": {"TITLE": "Delete the integration", "MESSAGE": "Are you sure you want to delete the integration? Doing so will result in the loss of access to conversations on your Slack workspace."}, "HELP_TEXT": {"TITLE": "Using Slack Integration", "BODY": "With this integration, all of your incoming conversations will be synced to the ***{selectedChannelName}*** channel in your Slack workspace. You can manage all your customer conversations right within the channel and never miss a message.\n\nHere are the main features of the integration:\n\n**Respond to conversations from within Slack:** To respond to a conversation in the ***{selectedChannelName}*** Slack channel, simply type out your message and send it as a thread. This will create a response back to the customer through AI비서 오팀장. It's that simple!\n\n **Create private notes:** If you want to create private notes instead of replies, start your message with ***`note:`***. This ensures that your message is kept private and won't be visible to the customer.\n\n**Associate an agent profile:** If the person who replied on Slack has an agent profile in AI비서 오팀장 under the same email, the replies will be associated with that agent profile automatically. This means you can easily track who said what and when. On the other hand, when the replier doesn't have an associated agent profile, the replies will appear from the bot profile to the customer.", "SELECTED": "selected"}, "SELECT_CHANNEL": {"OPTION_LABEL": "Select a channel", "UPDATE": "업데이트", "BUTTON_TEXT": "Connect channel", "DESCRIPTION": "Your Slack workspace is now linked with AI비서 오팀장. However, the integration is currently inactive. To activate the integration and connect a channel to AI비서 오팀장, please click the button below.\n\n**Note:** If you are attempting to connect a private channel, add the AI비서 오팀장 app to the Slack channel before proceeding with this step.", "ATTENTION_REQUIRED": "Attention required", "EXPIRED": "Your Slack integration has expired. To continue receiving messages on Slack, please delete the integration and connect your workspace again."}, "UPDATE_ERROR": "There was an error updating the integration, please try again", "UPDATE_SUCCESS": "The channel is connected successfully", "FAILED_TO_FETCH_CHANNELS": "There was an error fetching the channels from Slack, please try again"}, "DYTE": {"CLICK_HERE_TO_JOIN": "참여하려면 여기를 클릭하세요", "LEAVE_THE_ROOM": "방 나가기", "START_VIDEO_CALL_HELP_TEXT": "고객과 새로운 화상 통화 시작", "JOIN_ERROR": "통화 참여 중 오류가 발생했습니다. 다시 시도해주세요", "CREATE_ERROR": "미팅 링크 생성 중 오류가 발생했습니다. 다시 시도해주세요"}, "OPEN_AI": {"AI_ASSIST": "AI 도우미", "WITH_AI": "AI로 {option}", "OPTIONS": {"REPLY_SUGGESTION": "답변 제안", "SUMMARIZE": "요약", "REPHRASE": "글쓰기 개선", "FIX_SPELLING_GRAMMAR": "맞춤법 및 문법 수정", "SHORTEN": "줄이기", "EXPAND": "확장", "MAKE_FRIENDLY": "친근한 톤으로 변경", "MAKE_FORMAL": "공식적인 톤 사용", "SIMPLIFY": "단순화"}, "ASSISTANCE_MODAL": {"DRAFT_TITLE": "초안 내용", "GENERATED_TITLE": "생성된 내용", "AI_WRITING": "AI가 작성 중", "BUTTONS": {"APPLY": "이 제안 사용", "CANCEL": "취소"}}, "CTA_MODAL": {"TITLE": "OpenAI와 통합", "DESC": "OpenAI의 GPT 모델로 대시보드에 고급 AI 기능을 추가하세요. 시작하려면 OpenAI 계정의 API 키를 입력하세요.", "KEY_PLACEHOLDER": "OpenAI API 키 입력", "BUTTONS": {"NEED_HELP": "도움이 필요하신가요?", "DISMISS": "닫기", "FINISH": "설정 완료"}, "DISMISS_MESSAGE": "나중에 언제든지 OpenAI 통합을 설정할 수 있습니다.", "SUCCESS_MESSAGE": "OpenAI 통합이 성공적으로 설정되었습니다"}, "TITLE": "AI로 개선", "SUMMARY_TITLE": "AI로 요약", "REPLY_TITLE": "AI로 답변 제안", "SUBTITLE": "현재 초안을 기반으로 AI를 사용하여 개선된 답변이 생성됩니다.", "TONE": {"TITLE": "톤", "OPTIONS": {"PROFESSIONAL": "전문적", "FRIENDLY": "친근한"}}, "BUTTONS": {"GENERATE": "생성", "GENERATING": "생성 중...", "CANCEL": "취소"}, "GENERATE_ERROR": "내용 처리 중 오류가 발생했습니다. 다시 시도해주세요"}, "DELETE": {"BUTTON_TEXT": "삭제", "API": {"SUCCESS_MESSAGE": "통합이 성공적으로 삭제됨."}}, "CONNECT": {"BUTTON_TEXT": "연결"}, "DASHBOARD_APPS": {"TITLE": "대시보드 앱", "HEADER_BTN_TXT": "새 대시보드 앱 추가", "SIDEBAR_TXT": "<p><b>대시보드 앱</b></p><p>대시보드 앱을 사용하면 조직이 고객 지원 상담원에게 맥락을 제공하기 위해 AI비서 오팀장 대시보드에 애플리케이션을 임베드할 수 있습니다. 이 기능을 통해 독립적으로 애플리케이션을 만들고 사용자 정보, 주문 내역 또는 이전 결제 내역을 제공하기 위해 대시보드에 임베드할 수 있습니다.</p><p>AI비서 오팀장 대시보드에서 애플리케이션을 임베드하면, 애플리케이션은 대화와 연락처의 맥락을 윈도우 이벤트로 받게 됩니다. 페이지에서 메시지 이벤트에 대한 리스너를 구현하여 맥락을 받으세요.</p><p>새 대시보드 앱을 추가하려면 '새 대시보드 앱 추가' 버튼을 클릭하세요.</p>", "DESCRIPTION": "대시보드 앱을 사용하면 조직이 고객 지원 상담원에게 맥락을 제공하기 위해 대시보드에 애플리케이션을 임베드할 수 있습니다. 이 기능을 통해 독립적으로 애플리케이션을 만들고 사용자 정보, 주문 내역 또는 이전 결제 내역을 제공할 수 있습니다.", "LEARN_MORE": "대시보드 앱에 대해 자세히 알아보기", "LIST": {"404": "이 계정에는 아직 구성된 대시보드 앱이 없습니다", "LOADING": "대시보드 앱을 불러오는 중...", "TABLE_HEADER": {"NAME": "이름", "ENDPOINT": "엔드포인트"}, "EDIT_TOOLTIP": "앱 수정", "DELETE_TOOLTIP": "앱 삭제"}, "FORM": {"TITLE_LABEL": "이름", "TITLE_PLACEHOLDER": "대시보드 앱의 이름을 입력하세요", "TITLE_ERROR": "대시보드 앱의 이름이 필요합니다", "URL_LABEL": "엔드포인트", "URL_PLACEHOLDER": "앱이 호스팅된 엔드포인트 URL을 입력하세요", "URL_ERROR": "유효한 URL이 필요합니다"}, "CREATE": {"HEADER": "새 대시보드 앱 추가", "FORM_SUBMIT": "보내기", "FORM_CANCEL": "취소", "API_SUCCESS": "대시보드 앱이 성공적으로 구성되었습니다", "API_ERROR": "앱을 생성할 수 없습니다. 나중에 다시 시도해주세요"}, "UPDATE": {"HEADER": "대시보드 앱 수정", "FORM_SUBMIT": "업데이트", "FORM_CANCEL": "취소", "API_SUCCESS": "대시보드 앱이 성공적으로 업데이트되었습니다", "API_ERROR": "앱을 업데이트할 수 없습니다. 나중에 다시 시도해주세요"}, "DELETE": {"CONFIRM_YES": "예, 삭제합니다", "CONFIRM_NO": "아니요, 유지합니다", "TITLE": "삭제 확인", "MESSAGE": "앱을 삭제하시겠습니까 - {appName}?", "API_SUCCESS": "대시보드 앱이 성공적으로 삭제되었습니다", "API_ERROR": "앱을 삭제할 수 없습니다. 나중에 다시 시도해주세요"}}, "LINEAR": {"ADD_OR_LINK_BUTTON": "Linear 이슈 생성/연결", "LOADING": "Linear 이슈를 불러오는 중...", "LOADING_ERROR": "Linear 이슈를 불러오는 중 오류가 발생했습니다. 다시 시도해주세요", "CREATE": "만들기", "LINK": {"SEARCH": "이슈 검색", "SELECT": "이슈 선택", "TITLE": "링크", "EMPTY_LIST": "Linear 이슈를 찾을 수 없습니다", "LOADING": "로딩 중", "ERROR": "Linear 이슈를 불러오는 중 오류가 발생했습니다. 다시 시도해주세요", "LINK_SUCCESS": "이슈가 성공적으로 연결되었습니다", "LINK_ERROR": "이슈 연결 중 오류가 발생했습니다. 다시 시도해주세요", "LINK_TITLE": "{name}과의 대화 (#{conversationId})"}, "ADD_OR_LINK": {"TITLE": "Linear 이슈 생성/연결", "DESCRIPTION": "대화에서 Linear 이슈를 생성하거나 기존 이슈를 연결하여 원활한 추적이 가능합니다.", "FORM": {"TITLE": {"LABEL": "제목", "PLACEHOLDER": "제목 입력", "REQUIRED_ERROR": "제목이 필요합니다"}, "DESCRIPTION": {"LABEL": "내용", "PLACEHOLDER": "내용 입력"}, "TEAM": {"LABEL": "팀", "PLACEHOLDER": "팀 선택", "SEARCH": "팀 검색", "REQUIRED_ERROR": "팀이 필요합니다"}, "ASSIGNEE": {"LABEL": "담당자", "PLACEHOLDER": "담당자 선택", "SEARCH": "담당자 검색"}, "PRIORITY": {"LABEL": "우선순위", "PLACEHOLDER": "우선순위 선택", "SEARCH": "우선순위 검색"}, "LABEL": {"LABEL": "라벨", "PLACEHOLDER": "라벨 선택", "SEARCH": "라벨 검색"}, "STATUS": {"LABEL": "상태", "PLACEHOLDER": "상태 선택", "SEARCH": "상태 검색"}, "PROJECT": {"LABEL": "프로젝트", "PLACEHOLDER": "프로젝트 선택", "SEARCH": "프로젝트 검색"}}, "CREATE": "만들기", "CANCEL": "취소", "CREATE_SUCCESS": "이슈가 성공적으로 생성되었습니다", "CREATE_ERROR": "이슈 생성 중 오류가 발생했습니다. 다시 시도해주세요", "LOADING_TEAM_ERROR": "팀을 불러오는 중 오류가 발생했습니다. 다시 시도해주세요", "LOADING_TEAM_ENTITIES_ERROR": "팀 엔티티를 불러오는 중 오류가 발생했습니다. 다시 시도해주세요"}, "ISSUE": {"STATUS": "상태", "PRIORITY": "우선순위", "ASSIGNEE": "담당자", "LABELS": "라벨", "CREATED_AT": "{createdAt}에 생성됨"}, "UNLINK": {"TITLE": "연결 해제", "SUCCESS": "이슈 연결이 성공적으로 해제되었습니다", "ERROR": "이슈 연결 해제 중 오류가 발생했습니다. 다시 시도해주세요"}, "DELETE": {"TITLE": "통합을 삭제하시겠습니까?", "MESSAGE": "통합을 삭제하시겠습니까?", "CONFIRM": "예, 삭제합니다", "CANCEL": "취소"}}}, "CAPTAIN": {"NAME": "Captain", "HEADER_KNOW_MORE": "자세히 알아보기", "COPILOT": {"SEND_MESSAGE": "메시지 보내기...", "LOADER": "Captain이 생각 중입니다", "YOU": "나", "USE": "이 기능 사용", "RESET": "초기화", "SELECT_ASSISTANT": "어시스턴트 선택"}, "PAYWALL": {"TITLE": "Captain AI를 사용하려면 업그레이드하세요", "AVAILABLE_ON": "Captain은 무료 플랜에서는 사용할 수 없습니다.", "UPGRADE_PROMPT": "어시스턴트, 코파일럿 등의 기능을 사용하려면 플랜을 업그레이드하세요.", "UPGRADE_NOW": "지금 업그레이드", "CANCEL_ANYTIME": "언제든지 플랜을 변경하거나 취소할 수 있습니다"}, "ENTERPRISE_PAYWALL": {"AVAILABLE_ON": "Captain AI 기능은 유료 플랜에서만 사용할 수 있습니다.", "UPGRADE_PROMPT": "어시스턴트, 코파일럿 등의 기능을 사용하려면 플랜을 업그레이드하세요.", "ASK_ADMIN": "업그레이드에 대해서는 관리자에게 문의하세요."}, "BANNER": {"RESPONSES": "응답 한도의 80% 이상을 사용했습니다. Captain AI를 계속 사용하려면 업그레이드하세요.", "DOCUMENTS": "문서 한도에 도달했습니다. Captain AI를 계속 사용하려면 업그레이드하세요."}, "FORM": {"CANCEL": "취소", "CREATE": "만들기", "EDIT": "업데이트"}, "ASSISTANTS": {"HEADER": "어시스턴트", "ADD_NEW": "새 어시스턴트 만들기", "DELETE": {"TITLE": "어시스턴트를 삭제하시겠습니까?", "DESCRIPTION": "이 작업은 영구적입니다. 이 어시스턴트를 삭제하면 모든 연결된 수신함에서 제거되며 생성된 모든 지식이 영구적으로 삭제됩니다.", "CONFIRM": "예, 삭제합니다", "SUCCESS_MESSAGE": "어시스턴트가 성공적으로 삭제되었습니다", "ERROR_MESSAGE": "어시스턴트 삭제 중 오류가 발생했습니다. 다시 시도해주세요."}, "FORM_DESCRIPTION": "아래 세부 정보를 입력하여 어시스턴트의 이름을 지정하고, 목적을 설명하며, 지원할 제품을 지정하세요.", "CREATE": {"TITLE": "어시스턴트 만들기", "SUCCESS_MESSAGE": "어시스턴트가 성공적으로 생성되었습니다", "ERROR_MESSAGE": "어시스턴트 생성 중 오류가 발생했습니다. 다시 시도해주세요."}, "FORM": {"NAME": {"LABEL": "어시스턴트 이름", "PLACEHOLDER": "어시스턴트의 이름을 입력하세요", "ERROR": "어시스턴트의 이름을 입력해주세요"}, "DESCRIPTION": {"LABEL": "어시스턴트 설명", "PLACEHOLDER": "이 어시스턴트가 어떻게, 어디서 사용될지 설명하세요", "ERROR": "설명이 필요합니다"}, "PRODUCT_NAME": {"LABEL": "제품 이름", "PLACEHOLDER": "이 어시스턴트가 설계된 제품의 이름을 입력하세요", "ERROR": "제품 이름이 필요합니다"}, "FEATURES": {"TITLE": "특징", "ALLOW_CONVERSATION_FAQS": "해결된 대화에서 FAQs 생성", "ALLOW_MEMORIES": "고객 상호작용에서 중요한 세부 정보를 메모리로 캡처"}, "DATASETS": {"TITLE": "지식 베이스", "HELP": "지식 베이스는 도우미가 더 전문적인 질문에 대답할 수 있도록 돕습니다. 선택적입니다.", "NONE": "없음"}}, "EDIT": {"TITLE": "어시스턴트 업데이트", "SUCCESS_MESSAGE": "어시스턴트가 성공적으로 업데이트되었습니다", "ERROR_MESSAGE": "어시스턴트 업데이트 중 오류가 발생했습니다. 다시 시도해주세요."}, "OPTIONS": {"EDIT_ASSISTANT": "어시스턴트 수정", "DELETE_ASSISTANT": "어시스턴트 삭제", "VIEW_CONNECTED_INBOXES": "연결된 수신함 보기"}, "EMPTY_STATE": {"TITLE": "사용 가능한 어시스턴트가 없습니다", "SUBTITLE": "어시스턴트를 만들어 사용자에게 빠르고 정확한 응답을 제공하세요. 도움말 문서와 과거 대화에서 학습할 수 있습니다.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain 어시스턴트", "NOTE": "Captain 어시스턴트는 고객과 직접 소통하며, 도움말 문서와 과거 대화에서 학습하여 즉각적이고 정확한 응답을 제공합니다. 초기 문의를 처리하고 필요할 때 상담원에게 전달하기 전에 빠른 해결책을 제공합니다."}}}, "DOCUMENTS": {"HEADER": "문서", "ADD_NEW": "새 문서 만들기", "RELATED_RESPONSES": {"TITLE": "관련 FAQs", "DESCRIPTION": "이 FAQs는 문서에서 직접 생성됩니다."}, "FORM_DESCRIPTION": "문서를 지식 소스로 추가하려면 문서의 URL을 입력하고 연결할 어시스턴트를 선택하세요.", "CREATE": {"TITLE": "문서 추가", "SUCCESS_MESSAGE": "문서가 성공적으로 생성되었습니다", "ERROR_MESSAGE": "문서 생성 중 오류가 발생했습니다. 다시 시도해주세요."}, "FORM": {"URL": {"LABEL": "URL", "PLACEHOLDER": "문서의 URL을 입력하세요", "ERROR": "문서의 유효한 URL을 입력해주세요"}, "ASSISTANT": {"LABEL": "어시스턴트", "PLACEHOLDER": "어시스턴트 선택", "ERROR": "어시스턴트 필드는 필수입니다"}}, "DELETE": {"TITLE": "문서를 삭제하시겠습니까?", "DESCRIPTION": "이 작업은 영구적입니다. 이 문서를 삭제하면 생성된 모든 지식이 영구적으로 삭제됩니다.", "CONFIRM": "예, 삭제합니다", "SUCCESS_MESSAGE": "문서가 성공적으로 삭제되었습니다", "ERROR_MESSAGE": "문서 삭제 중 오류가 발생했습니다. 다시 시도해주세요."}, "OPTIONS": {"VIEW_RELATED_RESPONSES": "관련 응답 보기", "DELETE_DOCUMENT": "문서 삭제"}, "EMPTY_STATE": {"TITLE": "사용 가능한 문서가 없습니다", "SUBTITLE": "문서는 어시스턴트가 FAQs를 생성하는 데 사용됩니다. 문서를 가져와서 어시스턴트에게 맥락을 제공할 수 있습니다.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain 문서", "NOTE": "Captain의 문서는 어시스턴트를 위한 지식 자원으로 사용됩니다. 고객 지원 센터나 가이드를 연결하면 Captain이 내용을 분석하여 고객 문의에 대한 정확한 응답을 제공할 수 있습니다."}}}, "RESPONSES": {"HEADER": "FAQs", "ADD_NEW": "새 FAQ 만들기", "DOCUMENTABLE": {"CONVERSATION": "대화 #{id}"}, "SELECTED": "{count}개 선택됨", "BULK_APPROVE_BUTTON": "승인", "BULK_DELETE_BUTTON": "삭제", "BULK_APPROVE": {"SUCCESS_MESSAGE": "FAQs가 성공적으로 승인되었습니다", "ERROR_MESSAGE": "FAQs 승인 중 오류가 발생했습니다. 다시 시도해주세요."}, "BULK_DELETE": {"TITLE": "FAQs를 삭제하시겠습니까?", "DESCRIPTION": "선택한 FAQs를 삭제하시겠습니까? 이 작업은 취소할 수 없습니다.", "CONFIRM": "예, 모두 삭제합니다", "SUCCESS_MESSAGE": "FAQs가 성공적으로 삭제되었습니다", "ERROR_MESSAGE": "FAQs 삭제 중 오류가 발생했습니다. 다시 시도해주세요."}, "DELETE": {"TITLE": "FAQ를 삭제하시겠습니까?", "DESCRIPTION": "", "CONFIRM": "예, 삭제합니다", "SUCCESS_MESSAGE": "FAQ가 성공적으로 삭제되었습니다", "ERROR_MESSAGE": "FAQ 삭제 중 오류가 발생했습니다. 다시 시도해주세요."}, "FILTER": {"ASSISTANT": "어시스턴트: {selected}", "STATUS": "상태: {selected}", "ALL_ASSISTANTS": "모두"}, "STATUS": {"TITLE": "상태", "PENDING": "보내는 중", "APPROVED": "승인됨", "ALL": "모두"}, "FORM_DESCRIPTION": "지식 베이스에 질문과 해당하는 답변을 추가하고 연결할 어시스턴트를 선택하세요.", "CREATE": {"TITLE": "FAQ 추가", "SUCCESS_MESSAGE": "응답이 성공적으로 추가되었습니다.", "ERROR_MESSAGE": "응답 추가 중 오류가 발생했습니다. 다시 시도해주세요."}, "FORM": {"QUESTION": {"LABEL": "질문", "PLACEHOLDER": "여기에 질문을 입력하세요", "ERROR": "유효한 질문을 입력해주세요."}, "ANSWER": {"LABEL": "답변", "PLACEHOLDER": "여기에 답변을 입력하세요", "ERROR": "유효한 답변을 입력해주세요."}, "ASSISTANT": {"LABEL": "어시스턴트", "PLACEHOLDER": "어시스턴트 선택", "ERROR": "어시스턴트를 선택해주세요."}}, "EDIT": {"TITLE": "FAQ 업데이트", "SUCCESS_MESSAGE": "FAQ가 성공적으로 업데이트되었습니다", "ERROR_MESSAGE": "FAQ 업데이트 중 오류가 발생했습니다. 다시 시도해주세요", "APPROVE_SUCCESS_MESSAGE": "FAQ가 승인됨으로 표시되었습니다"}, "OPTIONS": {"APPROVE": "승인됨으로 표시", "EDIT_RESPONSE": "FAQ 수정", "DELETE_RESPONSE": "FAQ 삭제"}, "EMPTY_STATE": {"TITLE": "FAQ를 찾을 수 없습니다", "SUBTITLE": "FAQ는 어시스턴트가 고객의 질문에 빠르고 정확한 답변을 제공하는 데 도움이 됩니다. FAQ는 콘텐츠에서 자동으로 생성되거나 수동으로 추가할 수 있습니다.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain FAQ", "NOTE": "Captain FAQ는 지식 베이스에 없거나 자주 묻는 일반적인 고객 질문을 감지하고 관련 FAQ를 생성하여 지원을 개선합니다. 각 제안을 검토하고 승인 또는 거부를 결정할 수 있습니다."}}}, "INBOXES": {"HEADER": "연결된 수신함", "ADD_NEW": "새 수신함 연결", "OPTIONS": {"DISCONNECT": "연결 해제"}, "DELETE": {"TITLE": "수신함을 연결 해제하시겠습니까?", "DESCRIPTION": "", "CONFIRM": "예, 삭제합니다", "SUCCESS_MESSAGE": "수신함이 성공적으로 연결 해제되었습니다.", "ERROR_MESSAGE": "수신함 연결 해제 중 오류가 발생했습니다. 다시 시도해주세요."}, "FORM_DESCRIPTION": "어시스턴트와 연결할 수신함을 선택하세요.", "CREATE": {"TITLE": "수신함 연결", "SUCCESS_MESSAGE": "수신함이 성공적으로 연결되었습니다.", "ERROR_MESSAGE": "수신함 연결 중 오류가 발생했습니다. 다시 시도해주세요."}, "FORM": {"INBOX": {"LABEL": "받은 메시지함", "PLACEHOLDER": "어시스턴트를 배포할 수신함을 선택하세요.", "ERROR": "수신함 선택이 필요합니다."}}, "EMPTY_STATE": {"TITLE": "연결된 수신함이 없습니다", "SUBTITLE": "수신함을 연결하면 어시스턴트가 고객의 초기 질문을 처리하고 상담원에게 전달하기 전에 빠른 해결책을 제공할 수 있습니다."}}}}